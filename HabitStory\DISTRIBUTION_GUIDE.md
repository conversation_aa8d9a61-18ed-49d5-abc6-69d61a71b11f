# 📱 HabitStory - Guía de Distribución para Pruebas

Esta guía te ayudará a distribuir HabitStory para que otros usuarios puedan probar la aplicación en sus dispositivos iOS y Android.

## 🚀 Opciones de Distribución

### 1. Expo Go (Más Rápido - Recomendado para pruebas iniciales)

**Ventajas:**
- ✅ Configuración inmediata
- ✅ No requiere compilación
- ✅ Funciona en iOS y Android
- ✅ Actualizaciones instantáneas

**Pasos:**
1. Ejecuta el script de distribución:
   ```bash
   node scripts/distribute.js
   ```
   Selecciona opción 1

2. Comparte con tus testers:
   - Que instalen "Expo Go" desde App Store o Google Play
   - Que escaneen el código QR o visiten el enlace que aparece

### 2. APK para Android (Experiencia Real)

**Ventajas:**
- ✅ Experiencia idéntica a la app final
- ✅ No requiere Expo Go
- ✅ Funciona offline

**Pasos:**
1. Ejecuta el script de distribución:
   ```bash
   node scripts/distribute.js
   ```
   Selecciona opción 2

2. Espera el email con el enlace de descarga
3. Comparte el enlace del APK con tus testers

### 3. TestFlight para iOS (Requiere cuenta de desarrollador)

**Requisitos:**
- Cuenta de Apple Developer ($99/año)
- Configuración en App Store Connect

**Pasos:**
1. Ejecuta el script de distribución:
   ```bash
   node scripts/distribute.js
   ```
   Selecciona opción 3

2. Sube a TestFlight cuando esté listo
3. Invita testers por email

## 🛠️ Comandos Rápidos

```bash
# Distribución rápida con Expo Go
npm run publish

# Build Android APK
npm run build:android

# Build iOS
npm run build:ios

# Build ambas plataformas
npm run build:preview

# Script interactivo
node scripts/distribute.js
```

## 📋 Checklist Pre-Distribución

### Antes de distribuir, asegúrate de:

- [ ] **Backend funcionando**: El servidor backend debe estar ejecutándose
- [ ] **API Key configurada**: Gemini API key en el backend
- [ ] **Base de datos**: SQLite funcionando correctamente
- [ ] **Pruebas básicas**: La app funciona en tu dispositivo
- [ ] **Versión actualizada**: Incrementa version en app.json si es necesario

### Configuración del Backend

1. **Inicia el backend**:
   ```bash
   cd backend
   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Verifica que funciona**:
   - Visita: http://localhost:8000/health
   - Debe responder: `{"status": "healthy"}`

### Configuración de la App

1. **Actualiza la URL del backend** en el código si es necesario
2. **Verifica las credenciales** de API
3. **Prueba funcionalidades clave**:
   - Crear entrada de diario
   - Generar reporte semanal
   - Notificaciones

## 👥 Instrucciones para Testers

### Para Expo Go:

1. **Instala Expo Go**:
   - iOS: [App Store](https://apps.apple.com/app/expo-go/id982107779)
   - Android: [Google Play](https://play.google.com/store/apps/details?id=host.exp.exponent)

2. **Abre la app**:
   - Escanea el código QR que te compartieron
   - O introduce el enlace manualmente

3. **Empieza a probar**:
   - Crea tu primera entrada de diario
   - Explora las funcionalidades
   - Reporta cualquier problema

### Para APK (Android):

1. **Descarga el APK** del enlace compartido
2. **Permite instalación** de fuentes desconocidas si es necesario
3. **Instala y abre** la aplicación
4. **Empieza a probar**

### Para TestFlight (iOS):

1. **Acepta la invitación** por email
2. **Instala TestFlight** si no lo tienes
3. **Descarga HabitStory** desde TestFlight
4. **Empieza a probar**

## 🐛 Reporte de Problemas

Pide a tus testers que reporten problemas con:

### Información a incluir:
- **Dispositivo**: iPhone 12, Samsung Galaxy S21, etc.
- **Sistema operativo**: iOS 15.1, Android 12, etc.
- **Pasos para reproducir** el problema
- **Comportamiento esperado** vs **comportamiento actual**
- **Screenshots** si es posible

### Canales de reporte:
- Email
- WhatsApp
- Formulario de feedback en la app (si está implementado)

## 📊 Monitoreo

### Métricas a seguir:
- Número de instalaciones
- Crashes reportados
- Feedback de usuarios
- Funcionalidades más usadas
- Problemas comunes

### Herramientas:
- Expo Dashboard: https://expo.dev/accounts/irochep/projects/habit-story
- Logs del backend
- Feedback directo de usuarios

## 🔄 Actualizaciones

### Para Expo Go:
```bash
npm run publish
```
Los usuarios verán la actualización automáticamente.

### Para APK/TestFlight:
```bash
npm run build:preview
```
Necesitarás distribuir la nueva versión manualmente.

## 🆘 Solución de Problemas Comunes

### "No se puede conectar al servidor"
- Verifica que el backend esté ejecutándose
- Comprueba la URL del backend en la app
- Revisa la configuración de red

### "Error de API Key"
- Verifica que la Gemini API key esté configurada
- Comprueba que la key sea válida
- Revisa los límites de uso de la API

### "La app se cierra inesperadamente"
- Revisa los logs en Expo Dashboard
- Comprueba la compatibilidad del dispositivo
- Verifica que todas las dependencias estén instaladas

## 📞 Contacto

Si tienes problemas con la distribución:
- Revisa los logs en la consola
- Consulta la documentación de Expo
- Contacta al equipo de desarrollo

---

¡Listo para compartir HabitStory con el mundo! 🌟
