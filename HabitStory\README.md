# HabitStory 📖

**Your AI-powered personal journal that transforms daily reflections into personalized insights.**

HabitStory is a Progressive Web App (PWA) built with Expo + React Native that uses AI to analyze your daily journal entries and generate hyper-personalized weekly summaries. Works offline and can be installed on any device like a native app.

## ✨ Features

- **Daily Journaling**: Write free-form entries about your day
- **AI Analysis**: GPT-4 extracts habits, metrics, and insights from your text
- **Weekly Summaries**: Personalized HTML reports with trends and recommendations
- **Local Storage**: All data stored securely on your device using SQLite
- **Smart Notifications**: Daily reminders and weekly summary alerts
- **User Feedback**: Rate and improve AI-generated reports
- **Privacy First**: No external servers, your data never leaves your device

## 🚀 Quick Start

### Prerequisites

- Node.js (v16 or higher)
- Expo CLI (`npm install -g @expo/cli`)
- OpenAI API key ([Get one here](https://platform.openai.com/api-keys))

### Installation

1. **Clone and setup**
   ```bash
   git clone <repository-url>
   cd HabitStory
   npm install
   ```

2. **Start the development server**
   ```bash
   npx expo start
   ```

3. **Run on device**
   - Install Expo Go app on your phone
   - Scan the QR code from the terminal
   - Or use an emulator: `npx expo start --android` or `npx expo start --ios`

### First Time Setup

1. Enter your name when prompted
2. Add your OpenAI API key (stored securely on device)
3. Grant notification permissions for reminders
4. Start journaling!

## 🌐 Progressive Web App (PWA) Deployment

HabitStory can be deployed as a Progressive Web App that works offline and can be installed on any device.

### Quick PWA Deployment

1. **Build the web version**
   ```bash
   npm install
   npm run build:web
   ```

2. **Deploy to Vercel**
   ```bash
   npm install -g vercel
   vercel --prod
   ```

3. **Or deploy to Netlify**
   ```bash
   npm install -g netlify-cli
   netlify deploy --prod --dir=web-build
   ```

### PWA Features

- 📱 **Install to Home Screen**: Add HabitStory to your home screen on iOS and Android
- 🔄 **Offline Support**: Works without internet connection using Service Worker
- ⚡ **Fast Loading**: Cached resources for instant startup
- 🔔 **Push Notifications**: Receive reminders even when app is closed
- 🎨 **Native Feel**: Looks and feels like a native app

### Adding to Home Screen

**On iOS (Safari):**
1. Open HabitStory in Safari
2. Tap the Share button
3. Select "Add to Home Screen"
4. Tap "Add"

**On Android (Chrome):**
1. Open HabitStory in Chrome
2. Tap the menu (three dots)
3. Select "Add to Home screen"
4. Tap "Add"

**On Desktop:**
1. Open HabitStory in Chrome/Edge
2. Look for the install icon in the address bar
3. Click "Install HabitStory"

## 📱 How to Use

### Daily Journaling
1. Open the **Journal** tab
2. Write about your day in free-form text
3. Tap **"Analyze Entry"** to extract insights
4. View identified habits, metrics, and reflections

### Weekly Reports
1. Go to the **Reports** tab
2. Tap **"Generate Weekly Summary"** (requires at least one entry)
3. View your personalized HTML report
4. Provide feedback to improve future reports

### History & Settings
- **History**: Browse past entries and their analysis
- **Settings**: Update your name, API key, or logout

## 🔧 Technical Architecture

### Core Technologies

**Frontend (PWA)**
- **Framework**: React Native + Expo
- **Styling**: NativeWind (Tailwind CSS for React Native)
- **Database**: SQLite (expo-sqlite) + Web Storage API
- **PWA**: Service Worker, Web App Manifest, Offline Support
- **Navigation**: React Navigation v6
- **Notifications**: Expo Notifications + Web Push API
- **Security**: Expo SecureStore + Web Crypto API

**Backend (Optional)**
- **API**: FastAPI with Python
- **Database**: SQLite + SQLAlchemy (async)
- **AI Integration**: Google Gemini API (primary) + OpenAI GPT-4 (fallback)
- **Authentication**: JWT with token rotation
- **Deployment**: Docker + Vercel/Railway

### Database Schema
```sql
-- Journal entries
entries (id, date, text, habits, metrics, reflection, created_at)

-- Weekly reports
reports (id, week_start, week_end, html_content, feedback_rating, feedback_text, created_at)

-- User personality traits
user_traits (id, tone, style, traits, updated_at)

-- Report feedback
feedback (id, report_id, rating, text, created_at)
```

### Project Structure
```
src/
├── components/          # Reusable UI components
│   ├── AppNavigator.tsx # Main navigation setup
│   └── TabNavigator.tsx # Bottom tab navigation
├── screens/            # App screens
│   ├── JournalScreen.tsx
│   ├── HistoryScreen.tsx
│   ├── ReportScreen.tsx
│   ├── ReportWebViewScreen.tsx
│   ├── SettingsScreen.tsx
│   ├── LoginScreen.tsx
│   └── LoadingScreen.tsx
├── hooks/              # Custom React hooks
│   ├── useOpenAI.ts    # OpenAI API integration
│   ├── useEntries.ts   # Journal entries management
│   └── useReports.ts   # Reports management
├── context/            # React Context providers
│   ├── AuthContext.tsx # User authentication
│   └── AppContext.tsx  # App initialization
├── lib/                # Core utilities
│   ├── db.ts          # SQLite database layer
│   ├── openai.ts      # OpenAI API client
│   ├── prompts.ts     # GPT-4 prompt templates
│   ├── notifications.ts # Push notifications
│   └── global.css     # NativeWind styles
```

## 🧪 Testing

### Parse Test Script
Test the AI parsing functionality:

```bash
# Create a test entry
node scripts/parse-test.js "Today I woke up at 7 AM, went for a 30-minute run, had a healthy breakfast, and felt really productive at work. I meditated for 10 minutes before bed and felt grateful for the day."
```

### Manual Testing Checklist
- [ ] User onboarding flow
- [ ] Journal entry creation and analysis
- [ ] Weekly report generation
- [ ] Notification scheduling
- [ ] Data persistence across app restarts
- [ ] API key validation
- [ ] Error handling for network issues

## 🔐 Privacy & Security

- **Local-First**: All journal data stored on device using SQLite
- **Secure Storage**: API keys encrypted using Expo SecureStore
- **No Tracking**: No analytics, telemetry, or user tracking
- **OpenAI API**: Only journal text sent for analysis (no personal info)
- **Offline Capable**: Core functionality works without internet

## 📋 Configuration

### Notification Settings
- **Daily Reminder**: 9:00 PM (customizable)
- **Weekly Summary**: Sunday 10:00 AM (customizable)

### OpenAI Settings
- **Model**: GPT-4 (configurable in `src/lib/openai.ts`)
- **Max Tokens**: 2000 for parsing, 4000 for summaries
- **Temperature**: 0.7 for creative but consistent responses

## 🚨 Troubleshooting

### Common Issues

**"API key not working"**
- Ensure your OpenAI API key starts with `sk-`
- Check you have sufficient credits in your OpenAI account
- Test connection in Settings screen

**"Notifications not appearing"**
- Grant notification permissions when prompted
- Check device notification settings
- Ensure app is not in battery optimization mode (Android)

**"App crashes on startup"**
- Clear app data and restart
- Check Expo CLI version is up to date
- Verify all dependencies are installed

**"Analysis not working"**
- Check internet connection
- Verify OpenAI API key in Settings
- Try with shorter journal entries

### Debug Mode
Enable debug logging by setting `__DEV__` flag:
```javascript
// In src/lib/openai.ts
const DEBUG = __DEV__;
```

## 🚀 PWA Deployment Commands

```bash
# Quick setup and validation
npm run pwa:setup
npm run pwa:validate

# Deploy to different platforms
npm run deploy:vercel    # Deploy to Vercel
npm run deploy:netlify   # Deploy to Netlify
npm run deploy:surge     # Deploy to Surge.sh

# Manual deployment
npm install
npm run build:web
# Then upload web-build/ folder to your hosting provider
```

## 🔧 Environment Variables

For production deployment, set these environment variables:

```bash
# Vercel
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_org_id
VERCEL_PROJECT_ID=your_project_id

# Netlify
NETLIFY_AUTH_TOKEN=your_netlify_token
NETLIFY_SITE_ID=your_site_id

# Backend API (optional)
REACT_APP_API_URL=https://your-backend-api.com
```

## 📱 PWA Installation Guide

### For Users

1. **Visit the deployed PWA** in your browser
2. **Look for install prompt** or browser install icon
3. **Add to Home Screen** on mobile devices
4. **Enjoy offline functionality** and native app experience

### Installation Indicators

- 📱 **Mobile**: "Add to Home Screen" banner
- 💻 **Desktop**: Install icon in address bar
- 🔄 **Offline**: Works without internet connection
- 🔔 **Notifications**: Receive push notifications

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly (including PWA functionality)
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Google Gemini API for AI-powered insights
- OpenAI for GPT-4 API fallback
- Expo team for the amazing development platform
- React Native community for excellent libraries
- PWA community for offline-first principles

---

**Made with ❤️ for personal growth and self-reflection**

🌐 **Now available as a Progressive Web App - Install it on any device!**
