{"ci": {"collect": {"staticDistDir": "./web-build", "settings": {"preset": "desktop", "onlyCategories": ["performance", "accessibility", "best-practices", "seo", "pwa"]}}, "assert": {"assertions": {"categories:performance": ["warn", {"minScore": 0.8}], "categories:accessibility": ["warn", {"minScore": 0.9}], "categories:best-practices": ["warn", {"minScore": 0.9}], "categories:seo": ["warn", {"minScore": 0.9}], "categories:pwa": ["warn", {"minScore": 0.9}], "service-worker": "on", "installable-manifest": "on", "splash-screen": "on", "themed-omnibox": "on", "content-width": "on", "viewport": "on", "apple-touch-icon": "on", "maskable-icon": "on"}}, "upload": {"target": "temporary-public-storage"}}}