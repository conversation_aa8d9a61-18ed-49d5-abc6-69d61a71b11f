#!/usr/bin/env node

/**
 * HabitStory PWA Deployment Script
 * Automates the build and deployment process for the Progressive Web App
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`);
}

function execCommand(command, description) {
    log(`\n${colors.blue}📋 ${description}...${colors.reset}`);
    try {
        execSync(command, { stdio: 'inherit' });
        log(`${colors.green}✅ ${description} completed successfully${colors.reset}`);
        return true;
    } catch (error) {
        log(`${colors.red}❌ ${description} failed: ${error.message}${colors.reset}`);
        return false;
    }
}

function checkFile(filePath, description) {
    if (fs.existsSync(filePath)) {
        log(`${colors.green}✅ ${description} exists${colors.reset}`);
        return true;
    } else {
        log(`${colors.red}❌ ${description} not found at ${filePath}${colors.reset}`);
        return false;
    }
}

function createWebBuildStructure() {
    log(`\n${colors.magenta}🏗️  Creating web-build structure...${colors.reset}`);
    
    const webBuildDir = path.join(process.cwd(), 'web-build');
    
    if (!fs.existsSync(webBuildDir)) {
        fs.mkdirSync(webBuildDir, { recursive: true });
        log(`${colors.green}✅ Created web-build directory${colors.reset}`);
    }
    
    // Copy favicon if it doesn't exist
    const faviconSrc = path.join(process.cwd(), 'assets', 'favicon.png');
    const faviconDest = path.join(webBuildDir, 'favicon.png');
    
    if (fs.existsSync(faviconSrc) && !fs.existsSync(faviconDest)) {
        fs.copyFileSync(faviconSrc, faviconDest);
        log(`${colors.green}✅ Copied favicon.png${colors.reset}`);
    }
    
    return true;
}

function validatePWAFiles() {
    log(`\n${colors.magenta}🔍 Validating PWA files...${colors.reset}`);
    
    const requiredFiles = [
        { path: 'web-build/index.html', desc: 'Main HTML file' },
        { path: 'web-build/manifest.json', desc: 'Web App Manifest' },
        { path: 'web-build/sw.js', desc: 'Service Worker' },
        { path: 'web-build/favicon.png', desc: 'Favicon' }
    ];
    
    let allValid = true;
    
    for (const file of requiredFiles) {
        if (!checkFile(file.path, file.desc)) {
            allValid = false;
        }
    }
    
    return allValid;
}

function showDeploymentOptions() {
    log(`\n${colors.cyan}🚀 Deployment Options:${colors.reset}`);
    log(`
${colors.bright}1. Vercel Deployment:${colors.reset}
   npm install -g vercel
   vercel --prod

${colors.bright}2. Netlify Deployment:${colors.reset}
   npm install -g netlify-cli
   netlify deploy --prod --dir=web-build

${colors.bright}3. GitHub Pages:${colors.reset}
   - Push web-build folder to gh-pages branch
   - Enable GitHub Pages in repository settings

${colors.bright}4. Firebase Hosting:${colors.reset}
   npm install -g firebase-tools
   firebase init hosting
   firebase deploy

${colors.bright}5. Surge.sh:${colors.reset}
   npm install -g surge
   cd web-build && surge
`);
}

function updatePackageJson() {
    log(`\n${colors.blue}📦 Updating package.json scripts...${colors.reset}`);
    
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    
    if (!fs.existsSync(packageJsonPath)) {
        log(`${colors.red}❌ package.json not found${colors.reset}`);
        return false;
    }
    
    try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        
        // Add PWA deployment scripts if they don't exist
        if (!packageJson.scripts) {
            packageJson.scripts = {};
        }
        
        const newScripts = {
            'build:web': 'expo export --platform web',
            'deploy:vercel': 'npm run build:web && vercel --prod',
            'deploy:netlify': 'npm run build:web && netlify deploy --prod --dir=web-build',
            'deploy:surge': 'npm run build:web && cd web-build && surge',
            'pwa:validate': 'node scripts/deploy-pwa.js --validate'
        };
        
        let updated = false;
        for (const [script, command] of Object.entries(newScripts)) {
            if (!packageJson.scripts[script]) {
                packageJson.scripts[script] = command;
                updated = true;
                log(`${colors.green}✅ Added script: ${script}${colors.reset}`);
            }
        }
        
        if (updated) {
            fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
            log(`${colors.green}✅ package.json updated${colors.reset}`);
        }
        
        return true;
    } catch (error) {
        log(`${colors.red}❌ Failed to update package.json: ${error.message}${colors.reset}`);
        return false;
    }
}

function main() {
    const args = process.argv.slice(2);
    const isValidateOnly = args.includes('--validate');
    
    log(`${colors.bright}${colors.magenta}
🌐 HabitStory PWA Deployment Tool
=================================${colors.reset}`);
    
    if (isValidateOnly) {
        log(`${colors.yellow}🔍 Running validation only...${colors.reset}`);
        const isValid = validatePWAFiles();
        process.exit(isValid ? 0 : 1);
    }
    
    // Step 1: Create web-build structure
    if (!createWebBuildStructure()) {
        process.exit(1);
    }
    
    // Step 2: Update package.json
    if (!updatePackageJson()) {
        process.exit(1);
    }
    
    // Step 3: Install dependencies
    if (!execCommand('npm install', 'Installing dependencies')) {
        process.exit(1);
    }
    
    // Step 4: Build web version (this might fail due to expo-sqlite issue)
    log(`\n${colors.yellow}⚠️  Note: expo export might fail due to expo-sqlite web compatibility.${colors.reset}`);
    log(`${colors.yellow}   The PWA files are already created manually in web-build/.${colors.reset}`);
    
    const buildSuccess = execCommand('npm run build:web', 'Building web version');
    
    if (!buildSuccess) {
        log(`\n${colors.yellow}⚠️  Build failed, but PWA files are manually created.${colors.reset}`);
        log(`${colors.yellow}   You can still deploy the manually created PWA.${colors.reset}`);
    }
    
    // Step 5: Validate PWA files
    if (!validatePWAFiles()) {
        log(`\n${colors.red}❌ PWA validation failed. Please check the files.${colors.reset}`);
        process.exit(1);
    }
    
    // Step 6: Show deployment options
    showDeploymentOptions();
    
    log(`\n${colors.green}${colors.bright}🎉 PWA setup completed successfully!${colors.reset}`);
    log(`${colors.green}Your Progressive Web App is ready for deployment.${colors.reset}`);
    log(`\n${colors.cyan}💡 Quick deployment:${colors.reset}`);
    log(`${colors.cyan}   npm run deploy:vercel${colors.reset}`);
    log(`${colors.cyan}   npm run deploy:netlify${colors.reset}`);
}

// Run the script
if (require.main === module) {
    main();
}
