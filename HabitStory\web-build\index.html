<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="theme-color" content="#ffffff" />
    <meta name="description" content="Track your habits and get personalized insights with AI-powered analysis" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="HabitStory" />
    <meta name="mobile-web-app-capable" content="yes" />
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json" />
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="favicon.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="favicon.png" />
    <link rel="apple-touch-icon" href="favicon.png" />
    <link rel="shortcut icon" href="favicon.png" />
    
    <title>HabitStory - AI-Powered Habit Tracking</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #333;
            line-height: 1.6;
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .logo {
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .loading-text {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .loading-subtitle {
            font-size: 16px;
            opacity: 0.8;
            margin-bottom: 30px;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .install-prompt {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 15px;
            border-radius: 10px;
            display: none;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            z-index: 1000;
        }
        
        .install-prompt button {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
        }
        
        .install-prompt .close {
            background: transparent;
            color: #ccc;
            font-size: 18px;
            padding: 5px;
        }
        
        #root {
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading-container">
            <div class="logo">📊</div>
            <div class="loading-text">HabitStory</div>
            <div class="loading-subtitle">AI-Powered Habit Tracking</div>
            <div class="spinner"></div>
        </div>
    </div>
    
    <!-- Install Prompt -->
    <div id="installPrompt" class="install-prompt">
        <div>
            <strong>Install HabitStory</strong><br>
            Add to your home screen for the best experience
        </div>
        <div>
            <button id="installButton">Install</button>
            <button id="closePrompt" class="close">×</button>
        </div>
    </div>
    
    <!-- React App will be loaded here -->
    <script>
        // PWA Install Prompt
        let deferredPrompt;
        const installPrompt = document.getElementById('installPrompt');
        const installButton = document.getElementById('installButton');
        const closePrompt = document.getElementById('closePrompt');
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            installPrompt.style.display = 'flex';
        });
        
        installButton.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to the install prompt: ${outcome}`);
                deferredPrompt = null;
                installPrompt.style.display = 'none';
            }
        });
        
        closePrompt.addEventListener('click', () => {
            installPrompt.style.display = 'none';
        });
        
        // Hide install prompt if already installed
        window.addEventListener('appinstalled', () => {
            installPrompt.style.display = 'none';
            console.log('PWA was installed');
        });
        
        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);
                    })
                    .catch((registrationError) => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
        
        // Basic React-like functionality for demo
        setTimeout(() => {
            document.getElementById('root').innerHTML = `
                <div style="padding: 20px; max-width: 400px; margin: 0 auto; text-align: center;">
                    <h1 style="color: #667eea; margin-bottom: 20px;">🎉 HabitStory PWA</h1>
                    <p style="margin-bottom: 20px;">Your Progressive Web App is ready!</p>
                    <div style="background: #f5f5f5; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h3>✨ Features</h3>
                        <ul style="text-align: left; margin-top: 10px;">
                            <li>📱 Works offline</li>
                            <li>🏠 Add to home screen</li>
                            <li>🚀 Fast loading</li>
                            <li>🔄 Auto-updates</li>
                        </ul>
                    </div>
                    <p style="color: #666; font-size: 14px;">
                        This is a demo page. Your actual React Native app will load here after proper build configuration.
                    </p>
                </div>
            `;
        }, 2000);
    </script>
</body>
</html>
