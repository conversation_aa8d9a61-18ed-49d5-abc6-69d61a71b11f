@echo off
title HabitStory - Deploy for Testing

echo.
echo ========================================
echo 🚀 HabitStory - Deploy for Testing
echo ========================================
echo.

echo 📋 Checking prerequisites...

:: Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not found. Please install Node.js first.
    pause
    exit /b 1
)

:: Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python first.
    pause
    exit /b 1
)

echo ✅ Node.js and Python found

echo.
echo 🔧 Installing dependencies...
call npm install

echo.
echo 🔑 Checking backend configuration...
if not exist "backend\.env" (
    echo ⚠️  Backend .env file not found. Creating from template...
    copy "backend\.env.example" "backend\.env"
    echo.
    echo 📝 Please edit backend\.env and add your Gemini API key
    echo    Then run this script again.
    pause
    exit /b 1
)

echo ✅ Backend configuration found

echo.
echo 🚀 Starting backend...
start "HabitStory Backend" cmd /k "cd backend && pip install -r requirements.txt && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

echo ⏳ Waiting for backend to start...
timeout /t 10 /nobreak > nul

echo.
echo 🔍 Testing backend health...
curl -s http://localhost:8000/health > nul
if errorlevel 1 (
    echo ⚠️  Backend might not be ready yet. Please check manually: http://localhost:8000/health
) else (
    echo ✅ Backend is healthy
)

echo.
echo 📱 Choose distribution method:
echo.
echo 1. Expo Go (fastest, for immediate testing)
echo 2. Android APK (realistic experience)
echo 3. Both platforms
echo 4. Just start development server
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto expo_go
if "%choice%"=="2" goto android_apk
if "%choice%"=="3" goto both_platforms
if "%choice%"=="4" goto dev_server
goto invalid_choice

:expo_go
echo.
echo 📱 Publishing to Expo Go...
call npx expo publish
echo.
echo 🎉 Success! Share this with your testers:
echo    1. Install "Expo Go" from App Store or Google Play
echo    2. Scan the QR code above or visit the URL shown
echo.
goto end

:android_apk
echo.
echo 🤖 Building Android APK...
echo This will take several minutes...
call npx eas build --profile preview --platform android
echo.
echo 🎉 Android build started!
echo You'll receive an email when it's ready.
echo Check status at: https://expo.dev/accounts/irochep/projects/habit-story/builds
echo.
goto end

:both_platforms
echo.
echo 📱 Building for both platforms...
echo This will take several minutes...
call npx eas build --profile preview --platform all
echo.
echo 🎉 Builds started for both platforms!
echo You'll receive emails when they're ready.
echo Check status at: https://expo.dev/accounts/irochep/projects/habit-story/builds
echo.
goto end

:dev_server
echo.
echo 🔧 Starting development server...
echo.
echo 💡 Instructions:
echo    1. Wait for the QR code to appear
echo    2. Scan it with Expo Go on your mobile device
echo    3. Or press 'a' for Android emulator, 'i' for iOS simulator
echo.
call npm start
goto end

:invalid_choice
echo ❌ Invalid choice. Please run the script again.
goto end

:end
echo.
echo 📋 Next steps:
echo    1. Test the app yourself first
echo    2. Share with your testers
echo    3. Collect feedback
echo    4. Iterate and improve
echo.
echo 📖 For detailed instructions, see: QUICK_DISTRIBUTION_GUIDE.md
echo.
pause
