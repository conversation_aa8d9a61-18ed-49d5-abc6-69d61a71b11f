{"version": 2, "name": "habitstory-pwa", "builds": [{"src": "web-build/**", "use": "@vercel/static"}], "routes": [{"src": "/sw.js", "dest": "/web-build/sw.js", "headers": {"Service-Worker-Allowed": "/", "Cache-Control": "no-cache"}}, {"src": "/manifest.json", "dest": "/web-build/manifest.json", "headers": {"Content-Type": "application/manifest+json"}}, {"src": "/favicon.png", "dest": "/web-build/favicon.png", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/static/(.*)", "dest": "/web-build/static/$1", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/assets/(.*)", "dest": "/web-build/assets/$1", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/(.*)", "dest": "/web-build/index.html"}], "headers": [{"source": "/sw.js", "headers": [{"key": "Service-Worker-Allowed", "value": "/"}, {"key": "Cache-Control", "value": "no-cache"}]}, {"source": "/manifest.json", "headers": [{"key": "Content-Type", "value": "application/manifest+json"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "functions": {"web-build/index.html": {"includeFiles": "web-build/**"}}}