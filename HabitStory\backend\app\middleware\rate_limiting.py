"""
Rate limiting middleware for HabitStory API.
Implements per-user rate limiting to prevent abuse and manage costs.
"""

import time
import logging
from typing import Dict, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from ..config import settings

logger = logging.getLogger(__name__)


class RateLimitStore:
    """In-memory rate limit storage with sliding window."""
    
    def __init__(self):
        # Store request timestamps per user
        self.user_requests: Dict[str, deque] = defaultdict(lambda: deque())
        # Store last cleanup time
        self.last_cleanup = time.time()
        # Cleanup interval (5 minutes)
        self.cleanup_interval = 300
    
    def add_request(self, user_id: str, timestamp: float):
        """Add a request timestamp for a user."""
        self.user_requests[user_id].append(timestamp)
        self._cleanup_if_needed()
    
    def get_request_count(self, user_id: str, window_seconds: int) -> int:
        """Get number of requests in the time window."""
        current_time = time.time()
        cutoff_time = current_time - window_seconds
        
        # Remove old requests
        user_queue = self.user_requests[user_id]
        while user_queue and user_queue[0] < cutoff_time:
            user_queue.popleft()
        
        return len(user_queue)
    
    def _cleanup_if_needed(self):
        """Periodic cleanup of old data."""
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_old_data()
            self.last_cleanup = current_time
    
    def _cleanup_old_data(self):
        """Remove old request data to prevent memory leaks."""
        current_time = time.time()
        # Keep data for last hour only
        cutoff_time = current_time - 3600
        
        users_to_remove = []
        for user_id, requests in self.user_requests.items():
            # Remove old requests
            while requests and requests[0] < cutoff_time:
                requests.popleft()
            
            # Remove users with no recent requests
            if not requests:
                users_to_remove.append(user_id)
        
        for user_id in users_to_remove:
            del self.user_requests[user_id]
        
        if users_to_remove:
            logger.info(f"Cleaned up rate limit data for {len(users_to_remove)} inactive users")


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware with per-user limits."""
    
    def __init__(self, app):
        super().__init__(app)
        self.store = RateLimitStore()
        
        # Rate limits (requests per time window)
        self.limits = {
            "per_minute": settings.rate_limit_requests_per_minute,
            "per_hour": settings.rate_limit_requests_per_hour,
        }
        
        # Exempt paths (no rate limiting)
        self.exempt_paths = {
            "/health",
            "/",
            "/docs",
            "/openapi.json",
            "/api/v1/auth/login",  # Allow login attempts
        }
        
        # Strict paths (lower limits for expensive operations)
        self.strict_paths = {
            "/api/v1/entries/parse",
            "/api/v1/entries/batch-parse",
            "/api/v1/reports/weekly",
        }
    
    async def dispatch(self, request: Request, call_next):
        """Process request with rate limiting."""
        
        # Skip rate limiting for exempt paths
        if request.url.path in self.exempt_paths:
            return await call_next(request)
        
        # Get user identifier
        user_id = await self._get_user_id(request)
        if not user_id:
            # For unauthenticated requests, use IP address
            user_id = self._get_client_ip(request)
        
        # Check rate limits
        current_time = time.time()
        
        # Check per-minute limit
        minute_count = self.store.get_request_count(user_id, 60)
        minute_limit = self._get_minute_limit(request.url.path)
        
        if minute_count >= minute_limit:
            return self._create_rate_limit_response(
                "Rate limit exceeded: too many requests per minute",
                minute_limit,
                60,
                minute_count
            )
        
        # Check per-hour limit
        hour_count = self.store.get_request_count(user_id, 3600)
        hour_limit = self._get_hour_limit(request.url.path)
        
        if hour_count >= hour_limit:
            return self._create_rate_limit_response(
                "Rate limit exceeded: too many requests per hour",
                hour_limit,
                3600,
                hour_count
            )
        
        # Record the request
        self.store.add_request(user_id, current_time)
        
        # Process the request
        response = await call_next(request)
        
        # Add rate limit headers
        response.headers["X-RateLimit-Limit-Minute"] = str(minute_limit)
        response.headers["X-RateLimit-Remaining-Minute"] = str(max(0, minute_limit - minute_count - 1))
        response.headers["X-RateLimit-Limit-Hour"] = str(hour_limit)
        response.headers["X-RateLimit-Remaining-Hour"] = str(max(0, hour_limit - hour_count - 1))
        response.headers["X-RateLimit-Reset"] = str(int(current_time + 60))
        
        return response
    
    async def _get_user_id(self, request: Request) -> Optional[str]:
        """Extract user ID from JWT token."""
        try:
            auth_header = request.headers.get("authorization")
            if not auth_header or not auth_header.startswith("Bearer "):
                return None
            
            token = auth_header.split(" ")[1]
            
            # Import here to avoid circular imports
            from ..services.auth import auth_service
            payload = await auth_service.verify_access_token(token)
            return payload.get("user_id")
            
        except Exception:
            return None
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address."""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        if request.client:
            return request.client.host
        
        return "unknown"
    
    def _get_minute_limit(self, path: str) -> int:
        """Get per-minute limit for a path."""
        if path in self.strict_paths:
            return min(10, self.limits["per_minute"])  # Stricter limit for expensive operations
        return self.limits["per_minute"]
    
    def _get_hour_limit(self, path: str) -> int:
        """Get per-hour limit for a path."""
        if path in self.strict_paths:
            return min(100, self.limits["per_hour"])  # Stricter limit for expensive operations
        return self.limits["per_hour"]
    
    def _create_rate_limit_response(
        self,
        message: str,
        limit: int,
        window: int,
        current_count: int
    ) -> JSONResponse:
        """Create rate limit exceeded response."""
        
        reset_time = int(time.time() + window)
        
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "error": "Rate limit exceeded",
                "message": message,
                "limit": limit,
                "window_seconds": window,
                "current_count": current_count,
                "reset_time": reset_time,
                "retry_after": window
            },
            headers={
                "Retry-After": str(window),
                "X-RateLimit-Limit": str(limit),
                "X-RateLimit-Remaining": "0",
                "X-RateLimit-Reset": str(reset_time)
            }
        )


class APIKeyRateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting specifically for API key usage."""
    
    def __init__(self, app):
        super().__init__(app)
        self.store = RateLimitStore()
        
        # API-specific limits (more restrictive)
        self.api_limits = {
            "ai_requests_per_minute": 5,   # AI API calls per minute
            "ai_requests_per_hour": 100,   # AI API calls per hour
        }
        
        # Paths that consume AI API quota
        self.ai_paths = {
            "/api/v1/entries/parse",
            "/api/v1/entries/batch-parse",
            "/api/v1/reports/weekly",
        }
    
    async def dispatch(self, request: Request, call_next):
        """Process request with API-specific rate limiting."""
        
        # Only apply to AI API paths
        if request.url.path not in self.ai_paths:
            return await call_next(request)
        
        # Get user identifier
        user_id = await self._get_user_id(request)
        if not user_id:
            user_id = self._get_client_ip(request)
        
        # Check AI API limits
        current_time = time.time()
        
        # Check per-minute AI limit
        ai_minute_count = self.store.get_request_count(f"ai_{user_id}", 60)
        if ai_minute_count >= self.api_limits["ai_requests_per_minute"]:
            return self._create_api_limit_response(
                "AI API rate limit exceeded: too many AI requests per minute",
                self.api_limits["ai_requests_per_minute"],
                60
            )
        
        # Check per-hour AI limit
        ai_hour_count = self.store.get_request_count(f"ai_{user_id}", 3600)
        if ai_hour_count >= self.api_limits["ai_requests_per_hour"]:
            return self._create_api_limit_response(
                "AI API rate limit exceeded: too many AI requests per hour",
                self.api_limits["ai_requests_per_hour"],
                3600
            )
        
        # Record the AI request
        self.store.add_request(f"ai_{user_id}", current_time)
        
        # Process the request
        response = await call_next(request)
        
        # Add AI-specific headers
        response.headers["X-AI-RateLimit-Limit-Minute"] = str(self.api_limits["ai_requests_per_minute"])
        response.headers["X-AI-RateLimit-Remaining-Minute"] = str(
            max(0, self.api_limits["ai_requests_per_minute"] - ai_minute_count - 1)
        )
        
        return response
    
    async def _get_user_id(self, request: Request) -> Optional[str]:
        """Extract user ID from JWT token."""
        try:
            auth_header = request.headers.get("authorization")
            if not auth_header or not auth_header.startswith("Bearer "):
                return None
            
            token = auth_header.split(" ")[1]
            
            from ..services.auth import auth_service
            payload = await auth_service.verify_access_token(token)
            return payload.get("user_id")
            
        except Exception:
            return None
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address."""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        if request.client:
            return request.client.host
        
        return "unknown"
    
    def _create_api_limit_response(self, message: str, limit: int, window: int) -> JSONResponse:
        """Create API rate limit exceeded response."""
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "error": "AI API rate limit exceeded",
                "message": message,
                "limit": limit,
                "window_seconds": window,
                "suggestion": "Please wait before making more AI requests, or consider upgrading your plan"
            },
            headers={
                "Retry-After": str(window),
                "X-AI-RateLimit-Limit": str(limit),
                "X-AI-RateLimit-Remaining": "0"
            }
        )
