"""
AI Response Caching Service for HabitStory.
Implements intelligent caching to reduce API calls and improve performance.
"""

import hashlib
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, select, and_
from sqlalchemy.sql import func

from ..database import Base
from ..config import settings

logger = logging.getLogger(__name__)


class AIResponseCache(Base):
    """Database model for caching AI responses."""
    
    __tablename__ = "ai_response_cache"
    
    id = Column(Integer, primary_key=True, index=True)
    cache_key = Column(String(255), unique=True, nullable=False, index=True)
    
    # Request details
    provider = Column(String(50), nullable=False)
    model = Column(String(100), nullable=True)
    system_prompt_hash = Column(String(255), nullable=False)
    user_prompt_hash = Column(String(255), nullable=False)
    parameters_hash = Column(String(255), nullable=False)
    
    # Response data
    response_content = Column(Text, nullable=False)
    tokens_used = Column(Integer, nullable=True)
    input_tokens = Column(Integer, nullable=True)
    output_tokens = Column(Integer, nullable=True)
    
    # Cache metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_accessed = Column(DateTime(timezone=True), server_default=func.now())
    access_count = Column(Integer, default=1)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True)
    
    # Context for cache invalidation
    context_type = Column(String(50), nullable=True)  # "parsing", "recommendations", etc.
    user_id = Column(String(255), nullable=True, index=True)
    
    def __repr__(self):
        return f"<AIResponseCache(cache_key='{self.cache_key}', provider='{self.provider}')>"


class AICacheService:
    """Service for managing AI response caching."""
    
    def __init__(self):
        self.default_ttl_hours = 24  # Default cache TTL
        self.max_cache_size = 10000  # Maximum number of cached responses
        self.similarity_threshold = 0.95  # Threshold for considering prompts similar
    
    def _generate_cache_key(
        self,
        provider: str,
        system_prompt: str,
        user_prompt: str,
        parameters: Dict[str, Any]
    ) -> str:
        """Generate a unique cache key for the request."""
        # Create normalized parameter string
        normalized_params = json.dumps(parameters, sort_keys=True)
        
        # Create combined string for hashing
        combined = f"{provider}:{system_prompt}:{user_prompt}:{normalized_params}"
        
        # Generate SHA-256 hash
        return hashlib.sha256(combined.encode()).hexdigest()
    
    def _hash_text(self, text: str) -> str:
        """Generate hash for text content."""
        return hashlib.sha256(text.encode()).hexdigest()
    
    def _normalize_prompt(self, prompt: str) -> str:
        """Normalize prompt for better cache hits."""
        # Remove extra whitespace and normalize line endings
        normalized = ' '.join(prompt.split())
        return normalized.lower().strip()
    
    async def get_cached_response(
        self,
        db: AsyncSession,
        provider: str,
        system_prompt: str,
        user_prompt: str,
        parameters: Dict[str, Any],
        context_type: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Retrieve cached response if available."""
        try:
            # Normalize prompts for better matching
            normalized_system = self._normalize_prompt(system_prompt)
            normalized_user = self._normalize_prompt(user_prompt)
            
            # Generate cache key
            cache_key = self._generate_cache_key(
                provider, normalized_system, normalized_user, parameters
            )
            
            # Query cache
            result = await db.execute(
                select(AIResponseCache).where(
                    and_(
                        AIResponseCache.cache_key == cache_key,
                        AIResponseCache.is_active == True,
                        AIResponseCache.expires_at > datetime.utcnow()
                    )
                )
            )
            
            cached_response = result.scalar_one_or_none()
            
            if cached_response:
                # Update access statistics
                cached_response.last_accessed = datetime.utcnow()
                cached_response.access_count += 1
                await db.commit()
                
                logger.info(f"Cache hit for {provider} request (key: {cache_key[:16]}...)")
                
                return {
                    "content": cached_response.response_content,
                    "provider": provider,
                    "model": cached_response.model,
                    "tokens_used": cached_response.tokens_used,
                    "input_tokens": cached_response.input_tokens,
                    "output_tokens": cached_response.output_tokens,
                    "cached": True,
                    "cache_created": cached_response.created_at,
                    "cache_accessed": cached_response.access_count
                }
            
            logger.debug(f"Cache miss for {provider} request")
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving cached response: {e}")
            return None
    
    async def cache_response(
        self,
        db: AsyncSession,
        provider: str,
        system_prompt: str,
        user_prompt: str,
        parameters: Dict[str, Any],
        response_content: str,
        model: Optional[str] = None,
        tokens_used: Optional[int] = None,
        input_tokens: Optional[int] = None,
        output_tokens: Optional[int] = None,
        context_type: Optional[str] = None,
        user_id: Optional[str] = None,
        ttl_hours: Optional[int] = None
    ) -> bool:
        """Cache an AI response."""
        try:
            # Normalize prompts
            normalized_system = self._normalize_prompt(system_prompt)
            normalized_user = self._normalize_prompt(user_prompt)
            
            # Generate cache key and hashes
            cache_key = self._generate_cache_key(
                provider, normalized_system, normalized_user, parameters
            )
            
            system_hash = self._hash_text(normalized_system)
            user_hash = self._hash_text(normalized_user)
            params_hash = self._hash_text(json.dumps(parameters, sort_keys=True))
            
            # Calculate expiration
            ttl = ttl_hours or self.default_ttl_hours
            expires_at = datetime.utcnow() + timedelta(hours=ttl)
            
            # Create cache entry
            cache_entry = AIResponseCache(
                cache_key=cache_key,
                provider=provider,
                model=model,
                system_prompt_hash=system_hash,
                user_prompt_hash=user_hash,
                parameters_hash=params_hash,
                response_content=response_content,
                tokens_used=tokens_used,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                expires_at=expires_at,
                context_type=context_type,
                user_id=user_id
            )
            
            db.add(cache_entry)
            await db.commit()
            
            logger.info(f"Cached {provider} response (key: {cache_key[:16]}..., expires: {expires_at})")
            
            # Clean up old entries if cache is getting too large
            await self._cleanup_old_entries(db)
            
            return True
            
        except Exception as e:
            logger.error(f"Error caching response: {e}")
            await db.rollback()
            return False
    
    async def invalidate_cache(
        self,
        db: AsyncSession,
        context_type: Optional[str] = None,
        user_id: Optional[str] = None,
        provider: Optional[str] = None
    ) -> int:
        """Invalidate cached responses based on criteria."""
        try:
            # Build query conditions
            conditions = [AIResponseCache.is_active == True]
            
            if context_type:
                conditions.append(AIResponseCache.context_type == context_type)
            if user_id:
                conditions.append(AIResponseCache.user_id == user_id)
            if provider:
                conditions.append(AIResponseCache.provider == provider)
            
            # Get entries to invalidate
            result = await db.execute(
                select(AIResponseCache).where(and_(*conditions))
            )
            entries = result.scalars().all()
            
            # Mark as inactive
            count = 0
            for entry in entries:
                entry.is_active = False
                count += 1
            
            await db.commit()
            
            logger.info(f"Invalidated {count} cache entries")
            return count
            
        except Exception as e:
            logger.error(f"Error invalidating cache: {e}")
            await db.rollback()
            return 0
    
    async def _cleanup_old_entries(self, db: AsyncSession):
        """Clean up expired and least recently used cache entries."""
        try:
            # Remove expired entries
            expired_result = await db.execute(
                select(AIResponseCache).where(
                    AIResponseCache.expires_at < datetime.utcnow()
                )
            )
            expired_entries = expired_result.scalars().all()
            
            for entry in expired_entries:
                await db.delete(entry)
            
            # Check total cache size
            count_result = await db.execute(
                select(func.count(AIResponseCache.id)).where(
                    AIResponseCache.is_active == True
                )
            )
            total_count = count_result.scalar()
            
            # Remove oldest entries if over limit
            if total_count > self.max_cache_size:
                excess = total_count - self.max_cache_size
                
                old_entries_result = await db.execute(
                    select(AIResponseCache)
                    .where(AIResponseCache.is_active == True)
                    .order_by(AIResponseCache.last_accessed.asc())
                    .limit(excess)
                )
                old_entries = old_entries_result.scalars().all()
                
                for entry in old_entries:
                    await db.delete(entry)
            
            await db.commit()
            
            if expired_entries or (total_count > self.max_cache_size):
                logger.info(f"Cleaned up {len(expired_entries)} expired and {excess if total_count > self.max_cache_size else 0} old cache entries")
                
        except Exception as e:
            logger.error(f"Error cleaning up cache: {e}")
            await db.rollback()
    
    async def get_cache_stats(self, db: AsyncSession) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            # Total entries
            total_result = await db.execute(
                select(func.count(AIResponseCache.id)).where(
                    AIResponseCache.is_active == True
                )
            )
            total_entries = total_result.scalar()
            
            # Entries by provider
            provider_result = await db.execute(
                select(
                    AIResponseCache.provider,
                    func.count(AIResponseCache.id).label('count')
                )
                .where(AIResponseCache.is_active == True)
                .group_by(AIResponseCache.provider)
            )
            provider_stats = {row.provider: row.count for row in provider_result}
            
            # Cache hit rate (approximate)
            hit_result = await db.execute(
                select(func.avg(AIResponseCache.access_count))
                .where(AIResponseCache.is_active == True)
            )
            avg_access = hit_result.scalar() or 1
            
            return {
                "total_entries": total_entries,
                "max_cache_size": self.max_cache_size,
                "utilization": (total_entries / self.max_cache_size) * 100,
                "provider_distribution": provider_stats,
                "average_access_count": round(avg_access, 2),
                "default_ttl_hours": self.default_ttl_hours
            }
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {}


# Global cache service instance
ai_cache_service = AICacheService()
