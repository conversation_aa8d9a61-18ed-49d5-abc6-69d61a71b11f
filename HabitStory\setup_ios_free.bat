@echo off
title HabitStory - Setup for iOS (FREE)

echo.
echo ========================================
echo 📱 HabitStory - Setup for iOS (FREE)
echo ========================================
echo.

echo 🎯 This script will help you use HabitStory on iOS
echo    WITHOUT spending money and WITHOUT keeping your computer on!
echo.

echo 📋 What we'll do:
echo    1. Deploy your backend to the cloud (FREE)
echo    2. Configure your app to use the cloud backend
echo    3. Publish to Expo Go for iOS testing
echo.

pause

echo.
echo 🔍 Checking prerequisites...

:: Check if git is available
git --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Git not found. Please install Git first.
    echo    Download from: https://git-scm.com/download/win
    pause
    exit /b 1
)

:: Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not found. Please install Node.js first.
    echo    Download from: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Prerequisites found

echo.
echo 🚀 Step 1: Prepare for cloud deployment
echo.

:: Check if backend .env exists
if not exist "backend\.env" (
    echo ⚠️  Backend .env file not found. Creating from template...
    copy "backend\.env.example" "backend\.env"
    echo.
    echo 📝 Please edit backend\.env and make sure your Gemini API key is correct
    echo    File location: backend\.env
    echo.
    pause
)

echo ✅ Backend configuration ready

echo.
echo 📤 Step 2: Prepare Git repository
echo.

:: Initialize git if not already done
if not exist ".git" (
    echo 🔧 Initializing Git repository...
    git init
    git add .
    git commit -m "Initial commit for HabitStory"
    echo ✅ Git repository created
) else (
    echo ✅ Git repository already exists
    echo 🔄 Adding latest changes...
    git add .
    git commit -m "Prepare for cloud deployment" 2>nul || echo "No changes to commit"
)

echo.
echo ☁️  Step 3: Choose cloud deployment
echo.
echo 🆓 FREE Cloud Options:
echo.
echo 1. Railway (Recommended - Easiest setup)
echo 2. Render (Good alternative)
echo 3. Manual setup instructions
echo 4. Skip cloud deployment (use local only)
echo.

set /p cloud_choice="Enter your choice (1-4): "

if "%cloud_choice%"=="1" goto railway_setup
if "%cloud_choice%"=="2" goto render_setup
if "%cloud_choice%"=="3" goto manual_setup
if "%cloud_choice%"=="4" goto skip_cloud
goto invalid_choice

:railway_setup
echo.
echo 🚂 Railway Setup (FREE)
echo.
echo 📋 Follow these steps:
echo.
echo 1. Open: https://railway.app
echo 2. Sign up with GitHub (free account)
echo 3. Click "New Project" → "Deploy from GitHub repo"
echo 4. Select your HabitStory repository
echo 5. Railway will auto-deploy your backend
echo.
echo 🔧 Set these Environment Variables in Railway:
echo    GEMINI_API_KEY=AIzaSyBPGdbmlVQzY3XsVEXo_UPElTtVBiNSR_4
echo    SECRET_KEY=your_secure_jwt_key_here
echo    ENVIRONMENT=production
echo    DEBUG=false
echo.
echo 📝 After deployment, you'll get a URL like:
echo    https://habitstory-production.railway.app
echo.
echo ⏳ Please complete the Railway setup and then press any key...
pause

echo.
set /p railway_url="Enter your Railway app URL (e.g., https://your-app.railway.app): "

if "%railway_url%"=="" (
    echo ❌ URL is required. Please run the script again.
    pause
    exit /b 1
)

goto update_config

:render_setup
echo.
echo 🎨 Render Setup (FREE)
echo.
echo 📋 Follow these steps:
echo.
echo 1. Open: https://render.com
echo 2. Sign up with GitHub (free account)
echo 3. Click "New" → "Web Service"
echo 4. Connect your GitHub repository
echo 5. Configure:
echo    - Build Command: pip install -r requirements.txt
echo    - Start Command: python -m uvicorn app.main:app --host 0.0.0.0 --port $PORT
echo.
echo 🔧 Set Environment Variables:
echo    GEMINI_API_KEY=AIzaSyBPGdbmlVQzY3XsVEXo_UPElTtVBiNSR_4
echo    SECRET_KEY=your_secure_jwt_key_here
echo    ENVIRONMENT=production
echo    DEBUG=false
echo.
echo ⏳ Please complete the Render setup and then press any key...
pause

echo.
set /p render_url="Enter your Render app URL (e.g., https://your-app.onrender.com): "

if "%render_url%"=="" (
    echo ❌ URL is required. Please run the script again.
    pause
    exit /b 1
)

set railway_url=%render_url%
goto update_config

:manual_setup
echo.
echo 📖 Manual Setup Instructions
echo.
echo 🔧 You can deploy to any cloud provider:
echo    - Railway (recommended)
echo    - Render
echo    - Heroku
echo    - Vercel
echo    - Any VPS with Python support
echo.
echo 📋 Requirements:
echo    1. Upload your backend folder
echo    2. Install dependencies: pip install -r requirements.txt
echo    3. Set environment variables (see backend/.env.example)
echo    4. Start with: python -m uvicorn app.main:app --host 0.0.0.0 --port $PORT
echo.
echo ⏳ After deployment, press any key and enter your URL...
pause

echo.
set /p railway_url="Enter your deployed backend URL: "

if "%railway_url%"=="" (
    echo ❌ URL is required. Please run the script again.
    pause
    exit /b 1
)

goto update_config

:skip_cloud
echo.
echo ⚠️  Skipping cloud deployment
echo    Your app will only work when your computer is on
echo.
set railway_url=http://localhost:8000
goto update_config

:update_config
echo.
echo 🔧 Step 4: Update app configuration
echo.

:: Update the API configuration file
powershell -Command "(Get-Content 'src\config\api.ts') -replace 'https://your-app-name.railway.app', '%railway_url%' | Set-Content 'src\config\api.ts'"

echo ✅ App configuration updated with: %railway_url%

echo.
echo 📱 Step 5: Publish to Expo Go
echo.

echo 🔧 Installing dependencies...
call npm install

echo 📤 Publishing to Expo...
call npx expo publish

echo.
echo 🎉 SUCCESS! Your HabitStory app is ready for iOS!
echo.
echo 📱 To use on your iPhone:
echo    1. Install "Expo Go" from the App Store
echo    2. Scan the QR code above with your iPhone camera
echo    3. Your app will open in Expo Go
echo.
echo ✅ Benefits:
echo    - Works on iOS without Apple Developer account
echo    - Backend runs 24/7 in the cloud (FREE)
echo    - No need to keep your computer on
echo    - Updates automatically when you publish changes
echo.
echo 🔄 To update your app in the future:
echo    Just run: npm run publish
echo.
goto end

:invalid_choice
echo ❌ Invalid choice. Please run the script again.
goto end

:end
echo.
echo 📖 For detailed instructions, see: CLOUD_DEPLOYMENT_GUIDE.md
echo.
pause
