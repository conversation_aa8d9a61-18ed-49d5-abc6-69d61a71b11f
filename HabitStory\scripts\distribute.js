#!/usr/bin/env node

/**
 * HabitStory Distribution Script
 * Helps distribute the app for testing on iOS and Android
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function execCommand(command, description) {
  log(`\n${colors.blue}📋 ${description}...${colors.reset}`);
  try {
    const output = execSync(command, { stdio: 'inherit' });
    log(`${colors.green}✅ ${description} completed successfully${colors.reset}`);
    return output;
  } catch (error) {
    log(`${colors.red}❌ ${description} failed: ${error.message}${colors.reset}`);
    throw error;
  }
}

function showMenu() {
  log(`\n${colors.cyan}🚀 HabitStory Distribution Menu${colors.reset}`);
  log(`${colors.bright}Choose an option:${colors.reset}`);
  log(`${colors.yellow}1.${colors.reset} Quick test with Expo Go (fastest)`);
  log(`${colors.yellow}2.${colors.reset} Build Android APK for testing`);
  log(`${colors.yellow}3.${colors.reset} Build iOS for TestFlight`);
  log(`${colors.yellow}4.${colors.reset} Build both platforms`);
  log(`${colors.yellow}5.${colors.reset} Check build status`);
  log(`${colors.yellow}6.${colors.reset} Exit`);
}

function publishToExpoGo() {
  log(`\n${colors.magenta}📱 Publishing to Expo Go...${colors.reset}`);
  log(`${colors.yellow}This will allow users to test your app using the Expo Go app${colors.reset}`);
  
  execCommand('npx expo publish', 'Publishing to Expo');
  
  log(`\n${colors.green}🎉 Success! Your app is now available on Expo Go${colors.reset}`);
  log(`${colors.cyan}Share this with your testers:${colors.reset}`);
  log(`${colors.bright}1. Install "Expo Go" from App Store or Google Play${colors.reset}`);
  log(`${colors.bright}2. Scan the QR code or visit: exp://exp.host/@irochep/habit-story${colors.reset}`);
}

function buildAndroidAPK() {
  log(`\n${colors.green}🤖 Building Android APK...${colors.reset}`);
  log(`${colors.yellow}This will create an APK file you can share directly${colors.reset}`);
  
  execCommand('npx eas build --profile preview --platform android', 'Building Android APK');
  
  log(`\n${colors.green}🎉 Android APK build started!${colors.reset}`);
  log(`${colors.cyan}You'll receive an email when it's ready, or check: https://expo.dev/accounts/irochep/projects/habit-story/builds${colors.reset}`);
}

function buildIOS() {
  log(`\n${colors.blue}🍎 Building iOS for TestFlight...${colors.reset}`);
  log(`${colors.yellow}Note: You need an Apple Developer account for TestFlight distribution${colors.reset}`);
  
  execCommand('npx eas build --profile preview --platform ios', 'Building iOS app');
  
  log(`\n${colors.green}🎉 iOS build started!${colors.reset}`);
  log(`${colors.cyan}You'll receive an email when it's ready${colors.reset}`);
}

function buildBothPlatforms() {
  log(`\n${colors.magenta}📱 Building for both platforms...${colors.reset}`);
  
  execCommand('npx eas build --profile preview --platform all', 'Building for iOS and Android');
  
  log(`\n${colors.green}🎉 Builds started for both platforms!${colors.reset}`);
  log(`${colors.cyan}Check build status at: https://expo.dev/accounts/irochep/projects/habit-story/builds${colors.reset}`);
}

function checkBuildStatus() {
  log(`\n${colors.cyan}📊 Checking build status...${colors.reset}`);
  
  try {
    execCommand('npx eas build:list --limit=5', 'Fetching recent builds');
  } catch (error) {
    log(`${colors.yellow}💡 You can also check builds at: https://expo.dev/accounts/irochep/projects/habit-story/builds${colors.reset}`);
  }
}

function checkPrerequisites() {
  log(`${colors.cyan}🔍 Checking prerequisites...${colors.reset}`);
  
  // Check if EAS CLI is installed
  try {
    execSync('npx eas --version', { stdio: 'pipe' });
    log(`${colors.green}✅ EAS CLI is available${colors.reset}`);
  } catch (error) {
    log(`${colors.yellow}⚠️  Installing EAS CLI...${colors.reset}`);
    execCommand('npm install -g @expo/eas-cli', 'Installing EAS CLI');
  }
  
  // Check if user is logged in
  try {
    execSync('npx eas whoami', { stdio: 'pipe' });
    log(`${colors.green}✅ Logged in to Expo${colors.reset}`);
  } catch (error) {
    log(`${colors.yellow}⚠️  Please log in to Expo:${colors.reset}`);
    execCommand('npx eas login', 'Logging in to Expo');
  }
}

function main() {
  log(`${colors.bright}${colors.cyan}🏠 HabitStory Distribution Tool${colors.reset}`);
  log(`${colors.yellow}Making it easy to share your app with testers!${colors.reset}`);
  
  try {
    checkPrerequisites();
    
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    function askForChoice() {
      showMenu();
      rl.question(`\n${colors.bright}Enter your choice (1-6): ${colors.reset}`, (choice) => {
        switch (choice.trim()) {
          case '1':
            publishToExpoGo();
            break;
          case '2':
            buildAndroidAPK();
            break;
          case '3':
            buildIOS();
            break;
          case '4':
            buildBothPlatforms();
            break;
          case '5':
            checkBuildStatus();
            break;
          case '6':
            log(`${colors.green}👋 Goodbye!${colors.reset}`);
            rl.close();
            return;
          default:
            log(`${colors.red}❌ Invalid choice. Please try again.${colors.reset}`);
        }
        
        setTimeout(() => {
          askForChoice();
        }, 2000);
      });
    }
    
    askForChoice();
    
  } catch (error) {
    log(`${colors.red}❌ Error: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
