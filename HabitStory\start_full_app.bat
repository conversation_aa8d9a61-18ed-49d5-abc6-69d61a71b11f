@echo off
title HabitStory - Full App Launcher

echo.
echo ========================================
echo 🏠 HabitStory - Lanzador Completo
echo ========================================
echo.

echo 📋 Verificando dependencias del frontend...
call npm install

echo.
echo 🔧 Iniciando backend en segundo plano...
start "HabitStory Backend" cmd /k "cd backend && pip install -r requirements.txt && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

echo ⏳ Esperando que el backend se inicie...
timeout /t 5 /nobreak > nul

echo.
echo 📱 Iniciando aplicación Expo...
echo.
echo 💡 Instrucciones:
echo    1. Espera a que aparezca el código QR
echo    2. Escanéalo con Expo Go en tu móvil
echo    3. O presiona 'a' para Android, 'i' para iOS
echo.

call npm start

pause
