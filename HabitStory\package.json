{"name": "habitstory", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:web": "expo export --platform web", "test": "jest", "parse-test": "node scripts/parse-test.js", "db-reset": "node scripts/reset-db.js", "build": "expo build", "eject": "expo eject", "deploy:vercel": "npm run build:web && vercel --prod", "deploy:netlify": "npm run build:web && netlify deploy --prod --dir=web-build", "deploy:surge": "npm run build:web && cd web-build && surge", "pwa:setup": "node scripts/deploy-pwa.js", "pwa:validate": "node scripts/deploy-pwa.js --validate"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@types/react-native": "^0.72.8", "axios": "^1.10.0", "babel-plugin-inline-dotenv": "^1.7.0", "expo": "~53.0.16", "expo-blur": "^14.1.5", "expo-device": "^7.1.4", "expo-haptics": "^14.1.4", "expo-notifications": "^0.31.3", "expo-secure-store": "^14.2.3", "expo-sqlite": "^15.2.13", "expo-status-bar": "~2.2.3", "nativewind": "^2.0.11", "react": "19.0.0", "react-native": "0.79.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1", "react-native-webview": "^13.13.5", "tailwindcss": "^3.4.17", "expo-linear-gradient": "~14.1.5", "react-dom": "19.0.0", "react-native-web": "^0.20.0", "@expo/metro-runtime": "~5.0.4", "expo-updates": "~0.28.17"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}