# ☁️ HabitStory - Despliegue Gratuito en la Nube

## 🎯 Objetivo
Hacer que tu app HabitStory funcione en iOS **sin gastar dinero** y **sin necesidad de tener tu ordenador encendido**.

## 🆓 Opción Recomendada: Railway (100% Gratis)

### ✅ Ventajas de Railway:
- 🆓 **Completamente gratis** para proyectos pequeños
- 🚀 **Despliegue automático** desde GitHub
- 🔧 **Configuración mínima** requerida
- 📱 **URL permanente** para tu API
- 🔄 **Auto-redeploy** cuando haces cambios

### 📋 Pasos Detallados:

#### 1. **Preparar tu Código**
```bash
# Ya está listo! Los archivos necesarios ya están creados:
# - railway.json (configuración)
# - Procfile (comando de inicio)
# - requirements.txt (dependencias)
```

#### 2. **Subir a GitHub** (si no lo has hecho)
```bash
# Inicializar git (si no está hecho)
git init
git add .
git commit -m "Initial commit"

# Crear repositorio en GitHub y subir
git remote add origin https://github.com/tu-usuario/habitstory.git
git push -u origin main
```

#### 3. **Desplegar en Railway**
1. **Ve a**: https://railway.app
2. **Regístrate** con tu cuenta de GitHub (gratis)
3. **Clic en "New Project"**
4. **Selecciona "Deploy from GitHub repo"**
5. **Elige tu repositorio** HabitStory
6. **Railway detectará automáticamente** que es Python
7. **¡Listo!** Se desplegará automáticamente

#### 4. **Configurar Variables de Entorno**
En el dashboard de Railway:
1. **Ve a tu proyecto**
2. **Clic en "Variables"**
3. **Añade estas variables**:
   ```
   GEMINI_API_KEY=AIzaSyBPGdbmlVQzY3XsVEXo_UPElTtVBiNSR_4
   SECRET_KEY=tu_clave_jwt_segura_aqui
   ENVIRONMENT=production
   DEBUG=false
   ```

#### 5. **Obtener tu URL**
- Railway te dará una URL como: `https://habitstory-production.railway.app`
- **¡Esta URL funcionará 24/7 sin tu ordenador!**

## 📱 Actualizar tu App React Native

Una vez que tengas tu backend en la nube, actualiza tu app:

### 1. **Actualizar la URL del Backend**
Busca en tu código donde está la URL del backend y cámbiala:

```javascript
// Antes (local)
const API_URL = 'http://localhost:8000';

// Después (nube)
const API_URL = 'https://tu-app.railway.app';
```

### 2. **Publicar la Actualización**
```bash
npm run publish
```

### 3. **¡Listo para iOS!**
```bash
# Tu app ahora funcionará en iOS con Expo Go
# Y el backend estará siempre disponible en la nube
```

## 🔄 Alternativas Gratuitas

### Render.com
- 🆓 **Gratis** con algunas limitaciones
- 📋 **Pasos**:
  1. Ve a https://render.com
  2. Conecta GitHub
  3. Configura Web Service
  4. Build: `pip install -r requirements.txt`
  5. Start: `python -m uvicorn app.main:app --host 0.0.0.0 --port $PORT`

### Heroku (Limitado)
- ⚠️ **Gratis limitado** (duerme después de 30 min inactivo)
- 📋 **Pasos**:
  1. Ve a https://heroku.com
  2. Instala Heroku CLI
  3. `heroku create tu-app`
  4. `git push heroku main`

## 📱 Usar en iOS con Expo Go

### Una vez desplegado en la nube:

1. **Instala Expo Go** en tu iPhone (App Store)

2. **Publica tu app actualizada**:
   ```bash
   npm run publish
   ```

3. **Escanea el QR** con tu iPhone

4. **¡Tu app funcionará 24/7!** 
   - Backend en la nube (siempre disponible)
   - App en tu iPhone via Expo Go
   - No necesitas tu ordenador encendido

## 🔧 Configuración Avanzada

### Variables de Entorno Completas:
```bash
# Obligatorias
GEMINI_API_KEY=tu_api_key_aqui
SECRET_KEY=clave_jwt_minimo_64_caracteres

# Recomendadas
ENVIRONMENT=production
DEBUG=false
PORT=8000

# Opcionales
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_REQUESTS_PER_HOUR=1000
```

### Monitoreo:
- **Railway Dashboard**: Ver logs y métricas
- **Health Check**: `https://tu-app.railway.app/health`
- **API Docs**: `https://tu-app.railway.app/docs`

## 🛠️ Solución de Problemas

### "App no se conecta al servidor"
```bash
# 1. Verifica que el backend esté funcionando
curl https://tu-app.railway.app/health

# 2. Comprueba las variables de entorno en Railway
# 3. Revisa los logs en Railway dashboard
```

### "Error 500 en el servidor"
- Revisa los logs en Railway
- Verifica que la API key de Gemini sea correcta
- Comprueba que todas las dependencias estén en requirements.txt

### "La app se carga lento"
- Normal en servicios gratuitos
- Railway puede tardar unos segundos en "despertar"
- Considera usar un servicio de ping para mantenerlo activo

## 💰 Costos (TODO GRATIS)

| Servicio | Costo | Límites |
|----------|-------|---------|
| Railway | $0 | 500 horas/mes, 1GB RAM |
| Render | $0 | Duerme tras 15 min inactivo |
| Heroku | $0 | Duerme tras 30 min inactivo |
| Expo Go | $0 | Ilimitado para desarrollo |

## 🚀 Comando Rápido

```bash
# 1. Ejecuta el script de despliegue
deploy_to_cloud.bat

# 2. Sigue las instrucciones para Railway

# 3. Actualiza tu app con la nueva URL

# 4. Publica: npm run publish

# 5. ¡Usa en iOS con Expo Go!
```

## 🎉 Resultado Final

✅ **Backend en la nube** (24/7, gratis)  
✅ **App en iOS** via Expo Go (gratis)  
✅ **Sin ordenador encendido** necesario  
✅ **Sin gastos** de dinero  
✅ **Actualizaciones automáticas**  

**¡Tu app HabitStory funcionará perfectamente en iOS sin costos!** 📱✨
