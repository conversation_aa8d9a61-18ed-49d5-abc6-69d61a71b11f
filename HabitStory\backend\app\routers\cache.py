"""
Cache management router for HabitStory backend.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
from typing import Optional, Dict, Any
import logging

from ..database import get_db
from ..services.ai_client import unified_ai_service
from ..routers.auth import get_current_user
from ..models.auth import User

logger = logging.getLogger(__name__)
router = APIRouter()


class CacheInvalidateRequest(BaseModel):
    """Request model for cache invalidation."""
    context_type: Optional[str] = None
    user_id: Optional[str] = None
    provider: Optional[str] = None


@router.get("/cache/stats")
async def get_cache_stats(
    current_user: User = Depends(get_current_user)
):
    """
    Get cache statistics.
    
    Returns information about cache usage, hit rates, and performance.
    """
    try:
        stats = await unified_ai_service.get_cache_stats()
        
        return {
            "success": True,
            "stats": stats,
            "message": "Cache statistics retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get cache statistics: {str(e)}"
        )


@router.post("/cache/invalidate")
async def invalidate_cache(
    request: CacheInvalidateRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Invalidate cached responses based on criteria.
    
    This can be used to clear cache when:
    - User data changes significantly
    - AI models are updated
    - Cache becomes stale
    """
    try:
        invalidated_count = await unified_ai_service.invalidate_cache(
            context_type=request.context_type,
            user_id=request.user_id,
            provider=request.provider
        )
        
        return {
            "success": True,
            "invalidated_count": invalidated_count,
            "message": f"Successfully invalidated {invalidated_count} cache entries"
        }
        
    except Exception as e:
        logger.error(f"Error invalidating cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to invalidate cache: {str(e)}"
        )


@router.delete("/cache/user/{user_id}")
async def clear_user_cache(
    user_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Clear all cached responses for a specific user.
    
    Useful when a user requests data deletion or when
    their preferences change significantly.
    """
    try:
        # Only allow users to clear their own cache or admin users
        if current_user.user_id != user_id and current_user.plan != "ADMIN":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only clear your own cache"
            )
        
        invalidated_count = await unified_ai_service.invalidate_cache(
            user_id=user_id
        )
        
        return {
            "success": True,
            "invalidated_count": invalidated_count,
            "message": f"Successfully cleared cache for user {user_id}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing user cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear user cache: {str(e)}"
        )


@router.get("/cache/health")
async def cache_health_check():
    """
    Check cache system health.
    
    Returns cache configuration and basic health information.
    """
    try:
        provider_info = unified_ai_service.get_provider_info()
        
        health_info = {
            "cache_enabled": provider_info.get("cache_enabled", False),
            "ai_providers": provider_info.get("available_providers", []),
            "primary_provider": provider_info.get("primary_provider"),
            "status": "healthy"
        }
        
        # Try to get basic stats to verify cache is working
        if provider_info.get("cache_enabled"):
            try:
                stats = await unified_ai_service.get_cache_stats()
                health_info["cache_stats"] = {
                    "total_entries": stats.get("total_entries", 0),
                    "utilization": stats.get("utilization", 0)
                }
            except Exception as e:
                health_info["cache_warning"] = f"Cache stats unavailable: {str(e)}"
        
        return health_info
        
    except Exception as e:
        logger.error(f"Cache health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "cache_enabled": False
        }


@router.post("/cache/warm")
async def warm_cache(
    current_user: User = Depends(get_current_user)
):
    """
    Warm up the cache with common requests.
    
    This can be used to pre-populate the cache with
    frequently used AI responses.
    """
    try:
        # This is a placeholder for cache warming logic
        # In a real implementation, you might:
        # 1. Generate common parsing requests
        # 2. Pre-generate standard recommendations
        # 3. Cache frequently used prompts
        
        return {
            "success": True,
            "message": "Cache warming initiated",
            "note": "This is a placeholder endpoint for future cache warming functionality"
        }
        
    except Exception as e:
        logger.error(f"Error warming cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to warm cache: {str(e)}"
        )
