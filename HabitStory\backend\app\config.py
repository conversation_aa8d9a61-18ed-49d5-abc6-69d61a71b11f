"""
Configuration settings for HabitStory backend.
"""

from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    """Application settings."""
    
    # Database
    database_url: str = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///./habitstory.db")
    
    # Gemini API
    gemini_api_key: str = os.getenv("GEMINI_API_KEY", "")
    gemini_api_url: str = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent"
    gemini_timeout: int = 30
    gemini_max_retries: int = 3
    
    # Token limits by plan
    token_limits: dict = {
        "FREE": 10000,      # 10K tokens per month
        "BASIC": 50000,     # 50K tokens per month  
        "PREMIUM": 200000   # 200K tokens per month
    }
    
    # Rate limiting
    rate_limit_requests_per_minute: int = 60
    rate_limit_requests_per_hour: int = 1000
    
    # Security
    secret_key: str = os.getenv("SECRET_KEY", "dev-secret-key-change-in-production")
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 30

    # Token rotation settings
    auto_rotate_tokens: bool = True
    token_rotation_threshold_hours: int = 24  # Rotate if token used after this many hours
    
    # CORS
    allowed_origins: list = ["*"]
    
    # Logging
    log_level: str = "INFO"
    
    # Environment
    environment: str = os.getenv("ENVIRONMENT", "development")
    debug: bool = os.getenv("DEBUG", "true").lower() == "true"
    port: int = int(os.getenv("PORT", "8000"))
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
