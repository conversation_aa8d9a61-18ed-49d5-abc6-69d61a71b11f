"""
Module 7: Questions Service
Generates dynamic follow-up questions using Gemini.
"""

import json
import logging
from typing import Dict, List, Any, Optional

from .ai_client import unified_ai_service

logger = logging.getLogger(__name__)


class QuestionsService:
    """Service for generating personalized follow-up questions."""
    
    def __init__(self):
        self.ai_service = unified_ai_service
    
    async def generate_weekly_questions(
        self,
        user_id: str,
        entries: List[Dict[str, Any]],
        stats: Dict[str, Any],
        correlations: Dict[str, Any],
        user_traits: Dict[str, Any],
        validation_results: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Generate personalized weekly follow-up questions.
        
        Args:
            user_id: User identifier for token tracking
            entries: List of journal entries
            stats: Weekly statistics
            correlations: Correlation analysis results
            user_traits: User personality traits
            validation_results: Data validation results
            
        Returns:
            List of question dictionaries
        """
        try:
            # Define function schema for structured questions
            functions = [{
                "name": "generate_questions",
                "description": "Generate personalized follow-up questions based on user data",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "questions": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "category": {
                                        "type": "string",
                                        "enum": ["reflection", "exploration", "goal_setting", "pattern_analysis", "habit_improvement", "wellness_check"]
                                    },
                                    "question": {"type": "string"},
                                    "context": {"type": "string"},
                                    "purpose": {"type": "string"},
                                    "priority": {
                                        "type": "string",
                                        "enum": ["high", "medium", "low"]
                                    },
                                    "follow_up_prompts": {
                                        "type": "array",
                                        "items": {"type": "string"}
                                    }
                                },
                                "required": ["category", "question", "context", "purpose", "priority"]
                            },
                            "minItems": 3,
                            "maxItems": 8
                        }
                    },
                    "required": ["questions"]
                }
            }]
            
            # Create system prompt
            system_prompt = self._create_questions_system_prompt(user_traits)
            
            # Prepare user data
            user_prompt = self._create_questions_user_prompt(entries, stats, correlations, validation_results)
            
            # Generate questions
            response = await self.ai_service.generate_response(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                functions=functions,
                temperature=0.8,  # Higher temperature for creative question generation
                max_tokens=1500,
                user_id=user_id,
                context_type="questions"
            )
            
            # Parse response - try JSON first, then fallback to text parsing
            try:
                # Try to parse as JSON (structured response)
                parsed_data = json.loads(response.content)
                if isinstance(parsed_data, dict) and "questions" in parsed_data:
                    questions = parsed_data.get("questions", [])
                    return self._validate_questions(questions)
            except (json.JSONDecodeError, KeyError):
                pass

            # Fallback to text parsing
            return await self._parse_questions_from_text(response.content)
            
        except Exception as e:
            logger.error(f"Error generating questions: {e}")
            return self._get_fallback_questions(user_traits)
    
    async def generate_habit_questions(
        self,
        user_id: str,
        habit_name: str,
        habit_data: Dict[str, Any],
        user_traits: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Generate questions focused on a specific habit.
        
        Args:
            user_id: User identifier
            habit_name: Name of the habit
            habit_data: Statistics about the habit
            user_traits: User personality traits
            
        Returns:
            List of habit-focused questions
        """
        try:
            system_prompt = f"""You are a thoughtful coach who asks insightful questions to help people improve their habits.

USER PROFILE:
- Communication tone: {user_traits.get('tone', 'informal')}
- Style: {user_traits.get('style', 'conversational')}
- Personality: {json.dumps(user_traits.get('traits', {}), indent=2)}

Generate 2-4 specific, thoughtful questions about the habit '{habit_name}' that will help the user:
1. Understand their patterns better
2. Identify obstacles and opportunities
3. Develop strategies for improvement
4. Reflect on their motivation and progress

Match their communication style and personality. Make questions engaging and actionable."""
            
            user_prompt = f"""Habit: {habit_name}
Performance Data: {json.dumps(habit_data, indent=2)}

Generate insightful questions to help improve this habit."""
            
            response = await self.client.call_gemini(
                system=system_prompt,
                user=user_prompt,
                temperature=0.8,
                max_tokens=800,
                user_id=user_id
            )
            
            return await self._parse_questions_from_text(response["response"])
            
        except Exception as e:
            logger.error(f"Error generating habit questions: {e}")
            return [{
                "category": "habit_improvement",
                "question": f"What's one small change you could make to be more consistent with {habit_name}?",
                "context": f"Based on your {habit_name} tracking",
                "purpose": "Identify improvement opportunities",
                "priority": "medium",
                "follow_up_prompts": ["What obstacles do you face?", "When do you feel most motivated?"]
            }]
    
    async def generate_insight_questions(
        self,
        user_id: str,
        insight: Dict[str, Any],
        user_traits: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Generate questions based on a specific insight or correlation.
        
        Args:
            user_id: User identifier
            insight: Specific insight to explore
            user_traits: User personality traits
            
        Returns:
            List of insight-focused questions
        """
        try:
            system_prompt = f"""You are a curious coach who helps people explore their personal insights through thoughtful questions.

USER PROFILE:
- Tone: {user_traits.get('tone', 'informal')}
- Style: {user_traits.get('style', 'conversational')}
- Personality: {json.dumps(user_traits.get('traits', {}), indent=2)}

Based on the insight provided, generate 1-3 questions that help the user:
- Explore the insight more deeply
- Understand the implications
- Consider how to apply this knowledge
- Reflect on what it means for their growth

Use their communication style and make questions thought-provoking but not overwhelming."""
            
            user_prompt = f"""Insight to explore: {json.dumps(insight, indent=2)}

Generate questions that help the user explore this insight more deeply."""
            
            response = await self.client.call_gemini(
                system=system_prompt,
                user=user_prompt,
                temperature=0.7,
                max_tokens=600,
                user_id=user_id
            )
            
            return await self._parse_questions_from_text(response["response"])
            
        except Exception as e:
            logger.error(f"Error generating insight questions: {e}")
            return [{
                "category": "exploration",
                "question": "How might you use this insight to make positive changes in your life?",
                "context": "Based on your personal patterns",
                "purpose": "Apply insights practically",
                "priority": "medium",
                "follow_up_prompts": ["What would success look like?", "What's the first step?"]
            }]
    
    def _create_questions_system_prompt(self, user_traits: Dict[str, Any]) -> str:
        """Create the system prompt for question generation."""
        tone = user_traits.get('tone', 'informal')
        style = user_traits.get('style', 'conversational')
        traits = user_traits.get('traits', {})
        
        return f"""You are an expert life coach and mentor who asks powerful, personalized questions that inspire growth and self-discovery.

USER COMMUNICATION PROFILE:
- Tone: {tone}
- Style: {style}
- Personality Traits: {json.dumps(traits, indent=2)}

QUESTION GENERATION GUIDELINES:

1. **Personalization**:
   - Match their {tone} communication tone exactly
   - Use their {style} style throughout
   - Consider their personality traits when crafting questions
   - Make questions feel personally relevant and engaging

2. **Question Categories**:
   - **Reflection**: Deep thinking about experiences and patterns
   - **Exploration**: Discovering new aspects of themselves
   - **Goal Setting**: Planning and intention-setting
   - **Pattern Analysis**: Understanding their data and behaviors
   - **Habit Improvement**: Optimizing routines and practices
   - **Wellness Check**: Emotional and mental health awareness

3. **Question Quality**:
   - Open-ended questions that encourage thoughtful responses
   - Specific enough to be actionable
   - Thought-provoking but not overwhelming
   - Build on their actual data and patterns
   - Encourage growth mindset and self-compassion

4. **Priority Levels**:
   - **High**: Critical insights or concerning patterns that need attention
   - **Medium**: Important growth opportunities
   - **Low**: Interesting explorations for deeper self-knowledge

5. **Context and Purpose**:
   - Explain why each question matters
   - Connect to their specific data patterns
   - Show how answering will help their growth
   - Provide follow-up prompts to deepen exploration

6. **Tone Matching**:
   - If formal: Use professional, structured language
   - If informal: Use casual, friendly language
   - If analytical: Focus on data-driven questions
   - If emotional: Include feeling-focused questions

Generate 3-8 personalized questions that will genuinely help them grow and understand themselves better based on their weekly data."""
    
    def _create_questions_user_prompt(
        self,
        entries: List[Dict[str, Any]],
        stats: Dict[str, Any],
        correlations: Dict[str, Any],
        validation_results: Dict[str, Any]
    ) -> str:
        """Create the user data prompt for question generation."""
        # Summarize key patterns for question generation
        summary_parts = []
        
        # Trending metrics
        trends = stats.get('trends', {})
        improving_trends = [k.replace('_', ' ') for k, v in trends.items() if v == 'improving']
        declining_trends = [k.replace('_', ' ') for k, v in trends.items() if v == 'declining']
        
        if improving_trends:
            summary_parts.append(f"Improving areas: {', '.join(improving_trends)}")
        if declining_trends:
            summary_parts.append(f"Areas needing attention: {', '.join(declining_trends)}")
        
        # Habit patterns
        habit_metrics = stats.get('habit_metrics', {})
        strong_habits = []
        struggling_habits = []
        
        for habit, data in habit_metrics.items():
            if isinstance(data, dict):
                completion_rate = data.get('completion_rate', 0)
                if completion_rate > 0.8:
                    strong_habits.append(habit)
                elif completion_rate < 0.5:
                    struggling_habits.append(habit)
        
        if strong_habits:
            summary_parts.append(f"Strong habits: {', '.join(strong_habits)}")
        if struggling_habits:
            summary_parts.append(f"Habits to improve: {', '.join(struggling_habits)}")
        
        # Key insights
        insights = correlations.get('insights', [])
        if insights:
            key_insights = [insight.get('insight', '')[:100] + '...' for insight in insights[:2]]
            summary_parts.append(f"Key discoveries: {'; '.join(key_insights)}")
        
        # Data quality issues
        quality_flags = validation_results.get('quality_flags', [])
        if 'data_quality_concerns' in quality_flags:
            summary_parts.append("Data quality: Some inconsistencies detected")
        elif 'high_quality_data' in quality_flags:
            summary_parts.append("Data quality: Excellent consistency")
        
        # Recent reflections themes
        recent_reflections = []
        for entry in entries[-3:]:
            reflection = entry.get('reflection', '')
            if reflection:
                recent_reflections.append(reflection[:100] + '...' if len(reflection) > 100 else reflection)
        
        if recent_reflections:
            summary_parts.append(f"Recent themes: {'; '.join(recent_reflections)}")
        
        data_summary = "\n".join(summary_parts) if summary_parts else "Limited patterns available"
        
        return f"""USER DATA SUMMARY:
{data_summary}

DETAILED STATISTICS:
{json.dumps(stats.get('summary', {}), indent=2)}

KEY CORRELATIONS:
{json.dumps([insight.get('insight', '') for insight in correlations.get('insights', [])[:3]], indent=2)}

VALIDATION INSIGHTS:
Quality Score: {validation_results.get('data_quality_score', 0):.2f}
Issues: {', '.join(validation_results.get('quality_flags', []))}

Based on this comprehensive analysis, generate personalized questions that will help the user:
1. Explore their patterns and insights more deeply
2. Address areas that need attention
3. Build on their strengths
4. Set meaningful goals for growth
5. Reflect on their journey and progress

Focus on questions that are most relevant to their specific data patterns and will provide the most value for their personal development."""
    
    def _validate_questions(self, questions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate and clean questions."""
        validated = []
        
        for q in questions:
            if isinstance(q, dict) and 'question' in q:
                validated_q = {
                    "category": q.get("category", "reflection"),
                    "question": q.get("question", ""),
                    "context": q.get("context", "Based on your data patterns"),
                    "purpose": q.get("purpose", "Encourage self-reflection"),
                    "priority": q.get("priority", "medium"),
                    "follow_up_prompts": q.get("follow_up_prompts", [])
                }
                validated.append(validated_q)
        
        return validated[:8]  # Limit to 8 questions
    
    async def _parse_questions_from_text(self, text_response: str) -> List[Dict[str, Any]]:
        """Parse questions from text response."""
        questions = []
        
        try:
            # Try to find JSON in the response
            import re
            json_match = re.search(r'\[.*\]', text_response, re.DOTALL)
            if json_match:
                json_text = json_match.group()
                questions_data = json.loads(json_text)
                return self._validate_questions(questions_data)
        except:
            pass
        
        # Fallback: extract questions from text
        lines = text_response.split('\n')
        question_lines = []
        
        for line in lines:
            line = line.strip()
            if line and ('?' in line or line.lower().startswith(('what', 'how', 'why', 'when', 'where', 'which'))):
                # Clean up the line
                line = re.sub(r'^\d+\.?\s*', '', line)  # Remove numbering
                line = re.sub(r'^[-*]\s*', '', line)    # Remove bullet points
                if len(line) > 10:  # Reasonable question length
                    question_lines.append(line)
        
        # Convert to structured format
        for i, question_text in enumerate(question_lines[:6]):  # Limit to 6
            questions.append({
                "category": "reflection",
                "question": question_text,
                "context": "Based on your weekly patterns",
                "purpose": "Encourage deeper self-reflection",
                "priority": "medium",
                "follow_up_prompts": []
            })
        
        return questions if questions else self._get_fallback_questions({})
    
    def _get_fallback_questions(self, user_traits: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate fallback questions when AI generation fails."""
        tone = user_traits.get('tone', 'informal')
        
        if tone == 'formal':
            return [
                {
                    "category": "reflection",
                    "question": "What patterns in your data this week surprised you the most?",
                    "context": "Based on your weekly analysis",
                    "purpose": "Identify unexpected insights",
                    "priority": "high",
                    "follow_up_prompts": ["What might have caused this pattern?", "How can you leverage this insight?"]
                },
                {
                    "category": "goal_setting",
                    "question": "Which area of your life would benefit most from focused attention next week?",
                    "context": "Considering your current progress",
                    "purpose": "Prioritize improvement efforts",
                    "priority": "medium",
                    "follow_up_prompts": ["What specific steps will you take?", "How will you measure progress?"]
                }
            ]
        else:
            return [
                {
                    "category": "reflection",
                    "question": "What's one thing about this week that made you feel really proud?",
                    "context": "Looking at your progress and efforts",
                    "purpose": "Celebrate achievements and build confidence",
                    "priority": "high",
                    "follow_up_prompts": ["How can you do more of this?", "What made this possible?"]
                },
                {
                    "category": "exploration",
                    "question": "If you could change one thing about next week, what would it be?",
                    "context": "Based on this week's experiences",
                    "purpose": "Identify improvement opportunities",
                    "priority": "medium",
                    "follow_up_prompts": ["What's stopping you from making this change?", "What would success look like?"]
                }
            ]


# Global service instance
questions_service = QuestionsService()
