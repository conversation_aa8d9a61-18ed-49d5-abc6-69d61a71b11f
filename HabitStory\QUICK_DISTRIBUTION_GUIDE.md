# 🚀 HabitStory - Guía Rápida de Distribución

## ⚡ Inicio <PERSON> (5 minutos)

### 1. Preparar el Backend
```bash
# Opción A: Script automático (Windows)
start_backend.bat

# Opción B: Script automático (Mac/Linux)
./start_backend.sh

# Opción C: Manual
cd backend
pip install -r requirements.txt
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. Verificar Backend
Abre en tu navegador: http://localhost:8000/health
Debe mostrar: `{"status": "healthy"}`

### 3. Distribuir la App

#### Opción 1: Expo Go (MÁS RÁPIDO) ⚡
```bash
# Usar el script interactivo
npm run distribute
# Selecciona opción 1

# O directamente:
npm run publish
```

**Para tus testers:**
1. Instalar "Expo Go" desde App Store/Google Play
2. Escanear el código QR que aparece
3. ¡Listo para probar!

#### Opción 2: APK Android (MÁS REALISTA) 📱
```bash
# Usar el script interactivo
npm run distribute
# Selecciona opción 2

# O directamente:
npm run build:android
```

**Para tus testers:**
1. Recibirán un email con el enlace de descarga
2. Descargar e instalar el APK
3. ¡Listo para probar!

## 📋 Checklist Pre-Distribución

- [ ] Backend ejecutándose en http://localhost:8000
- [ ] Health check responde correctamente
- [ ] API Key de Gemini configurada
- [ ] App funciona en tu dispositivo
- [ ] Versión actualizada en app.json

## 🔧 Configuración Avanzada

### Variables de Entorno (backend/.env)
```bash
GEMINI_API_KEY=tu_api_key_aqui
SECRET_KEY=clave_secreta_jwt_aqui
ENVIRONMENT=development
DEBUG=true
```

### Configuración de Rate Limiting
```bash
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_REQUESTS_PER_HOUR=1000
```

## 📱 Instrucciones para Testers

### Con Expo Go:
1. **Instala Expo Go**: App Store (iOS) o Google Play (Android)
2. **Escanea el QR**: Que te compartieron
3. **Prueba la app**: Crea entradas, genera reportes

### Con APK (Android):
1. **Descarga el APK**: Del enlace compartido
2. **Permite instalación**: De fuentes desconocidas si es necesario
3. **Instala y prueba**: La aplicación

## 🐛 Solución de Problemas

### "No se puede conectar al servidor"
- ✅ Verifica que el backend esté ejecutándose
- ✅ Comprueba http://localhost:8000/health
- ✅ Revisa la configuración de red

### "Error de API Key"
- ✅ Verifica la API key de Gemini en backend/.env
- ✅ Comprueba que la key sea válida
- ✅ Revisa los límites de uso

### "La app se cierra"
- ✅ Revisa los logs en Expo Dashboard
- ✅ Comprueba la compatibilidad del dispositivo
- ✅ Verifica las dependencias

## 📊 Monitoreo

### Métricas a seguir:
- Número de usuarios probando
- Crashes reportados
- Feedback recibido
- Funcionalidades más usadas

### Herramientas:
- **Expo Dashboard**: https://expo.dev/accounts/irochep/projects/habit-story
- **Backend Logs**: En la consola donde ejecutas el backend
- **Cache Stats**: GET /api/v1/cache/stats

## 🔄 Actualizaciones

### Para Expo Go:
```bash
npm run publish
```
Los usuarios verán la actualización automáticamente.

### Para APK:
```bash
npm run build:android
```
Necesitarás distribuir la nueva versión.

## 📞 Comandos Útiles

```bash
# Distribución interactiva
npm run distribute

# Publicar en Expo Go
npm run publish

# Build Android
npm run build:android

# Build iOS
npm run build:ios

# Build ambas plataformas
npm run build:preview

# Iniciar backend
start_backend.bat  # Windows
./start_backend.sh # Mac/Linux

# Iniciar todo (backend + app)
start_full_app.bat # Windows
```

## 🎯 Próximos Pasos

1. **Recopilar feedback** de los testers
2. **Iterar** basado en comentarios
3. **Preparar para producción** cuando esté listo
4. **Publicar en stores** (App Store/Google Play)

---

¡Tu app HabitStory está lista para ser probada por otros usuarios! 🎉

**¿Necesitas ayuda?** Revisa los logs, consulta la documentación o contacta al equipo de desarrollo.
