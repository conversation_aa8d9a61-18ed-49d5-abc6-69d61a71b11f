/**
 * API Configuration for HabitStory
 * Handles backend URL configuration for different environments
 */

// Configuration for different environments
const API_CONFIG = {
  // Local development (when running backend on your computer)
  local: 'http://localhost:8000',
  
  // Cloud deployment (update this with your Railway/Render URL)
  production: 'https://your-app-name.railway.app', // 👈 UPDATE THIS!
  
  // You can add more environments as needed
  staging: 'https://your-staging-app.railway.app',
};

// Determine which environment to use
const getEnvironment = (): keyof typeof API_CONFIG => {
  // You can add logic here to detect environment
  // For now, we'll use a simple approach
  
  // If you want to force production (cloud) mode, uncomment this:
  // return 'production';
  
  // Auto-detect based on whether we're in development
  if (__DEV__) {
    return 'local'; // Use local backend during development
  } else {
    return 'production'; // Use cloud backend in published app
  }
};

// Get the current API base URL
export const API_BASE_URL = API_CONFIG[getEnvironment()];

// API endpoints
export const API_ENDPOINTS = {
  // Health check
  health: `${API_BASE_URL}/health`,
  
  // Authentication
  auth: {
    login: `${API_BASE_URL}/api/v1/auth/login`,
    refresh: `${API_BASE_URL}/api/v1/auth/refresh`,
    logout: `${API_BASE_URL}/api/v1/auth/logout`,
    me: `${API_BASE_URL}/api/v1/auth/me`,
  },
  
  // Entries
  entries: {
    parse: `${API_BASE_URL}/api/v1/entries/parse`,
    batchParse: `${API_BASE_URL}/api/v1/entries/batch-parse`,
    usage: `${API_BASE_URL}/api/v1/entries/usage`,
  },
  
  // Reports
  reports: {
    weekly: `${API_BASE_URL}/api/v1/reports/weekly`,
  },
  
  // Feedback
  feedback: {
    submit: `${API_BASE_URL}/api/v1/feedback/submit`,
  },
  
  // Cache management
  cache: {
    stats: `${API_BASE_URL}/api/v1/cache/stats`,
    invalidate: `${API_BASE_URL}/api/v1/cache/invalidate`,
    health: `${API_BASE_URL}/api/v1/cache/health`,
  },
};

// Helper function to check if backend is reachable
export const checkBackendHealth = async (): Promise<boolean> => {
  try {
    const response = await fetch(API_ENDPOINTS.health, {
      method: 'GET',
      timeout: 5000, // 5 second timeout
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.status === 'healthy';
    }
    
    return false;
  } catch (error) {
    console.warn('Backend health check failed:', error);
    return false;
  }
};

// Configuration info for debugging
export const getApiConfig = () => ({
  environment: getEnvironment(),
  baseUrl: API_BASE_URL,
  isDevelopment: __DEV__,
});

// Log current configuration (helpful for debugging)
console.log('🔧 API Configuration:', getApiConfig());
