#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo ""
echo "========================================"
echo "🚀 HabitStory - Deploy for Testing"
echo "========================================"
echo ""

echo "📋 Checking prerequisites..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js not found. Please install Node.js first.${NC}"
    exit 1
fi

# Check if Python is installed
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo -e "${RED}❌ Python not found. Please install Python first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js and Python found${NC}"

echo ""
echo "🔧 Installing dependencies..."
npm install

echo ""
echo "🔑 Checking backend configuration..."
if [ ! -f "backend/.env" ]; then
    echo -e "${YELLOW}⚠️  Backend .env file not found. Creating from template...${NC}"
    cp "backend/.env.example" "backend/.env"
    echo ""
    echo "📝 Please edit backend/.env and add your Gemini API key"
    echo "   Then run this script again."
    exit 1
fi

echo -e "${GREEN}✅ Backend configuration found${NC}"

echo ""
echo "🚀 Starting backend..."
# Start backend in background
cd backend
pip3 install -r requirements.txt > /dev/null 2>&1 || pip install -r requirements.txt > /dev/null 2>&1
python3 -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 > /dev/null 2>&1 &
BACKEND_PID=$!
cd ..

echo "⏳ Waiting for backend to start..."
sleep 10

echo ""
echo "🔍 Testing backend health..."
if curl -s http://localhost:8000/health > /dev/null; then
    echo -e "${GREEN}✅ Backend is healthy${NC}"
else
    echo -e "${YELLOW}⚠️  Backend might not be ready yet. Please check manually: http://localhost:8000/health${NC}"
fi

echo ""
echo "📱 Choose distribution method:"
echo ""
echo "1. Expo Go (fastest, for immediate testing)"
echo "2. Android APK (realistic experience)"
echo "3. Both platforms"
echo "4. Just start development server"
echo ""

read -p "Enter your choice (1-4): " choice

case $choice in
    1)
        echo ""
        echo "📱 Publishing to Expo Go..."
        npx expo publish
        echo ""
        echo -e "${GREEN}🎉 Success! Share this with your testers:${NC}"
        echo "   1. Install \"Expo Go\" from App Store or Google Play"
        echo "   2. Scan the QR code above or visit the URL shown"
        echo ""
        ;;
    2)
        echo ""
        echo "🤖 Building Android APK..."
        echo "This will take several minutes..."
        npx eas build --profile preview --platform android
        echo ""
        echo -e "${GREEN}🎉 Android build started!${NC}"
        echo "You'll receive an email when it's ready."
        echo "Check status at: https://expo.dev/accounts/irochep/projects/habit-story/builds"
        echo ""
        ;;
    3)
        echo ""
        echo "📱 Building for both platforms..."
        echo "This will take several minutes..."
        npx eas build --profile preview --platform all
        echo ""
        echo -e "${GREEN}🎉 Builds started for both platforms!${NC}"
        echo "You'll receive emails when they're ready."
        echo "Check status at: https://expo.dev/accounts/irochep/projects/habit-story/builds"
        echo ""
        ;;
    4)
        echo ""
        echo "🔧 Starting development server..."
        echo ""
        echo -e "${BLUE}💡 Instructions:${NC}"
        echo "   1. Wait for the QR code to appear"
        echo "   2. Scan it with Expo Go on your mobile device"
        echo "   3. Or press 'a' for Android emulator, 'i' for iOS simulator"
        echo ""
        npm start
        ;;
    *)
        echo -e "${RED}❌ Invalid choice. Please run the script again.${NC}"
        ;;
esac

echo ""
echo "📋 Next steps:"
echo "   1. Test the app yourself first"
echo "   2. Share with your testers"
echo "   3. Collect feedback"
echo "   4. Iterate and improve"
echo ""
echo "📖 For detailed instructions, see: QUICK_DISTRIBUTION_GUIDE.md"
echo ""

# Cleanup function
cleanup() {
    echo ""
    echo "🧹 Cleaning up..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
    fi
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup EXIT INT TERM
