@echo off
title HabitStory - Deploy to Cloud (Free)

echo.
echo ========================================
echo ☁️  HabitStory - Deploy to Cloud (FREE)
echo ========================================
echo.

echo 🎯 Choose your free cloud provider:
echo.
echo 1. Railway (Recommended - Easy setup)
echo 2. Render (Good alternative)
echo 3. Heroku (Classic option)
echo 4. Manual setup instructions
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto railway
if "%choice%"=="2" goto render
if "%choice%"=="3" goto heroku
if "%choice%"=="4" goto manual
goto invalid_choice

:railway
echo.
echo 🚂 Railway Deployment
echo.
echo 📋 Steps to deploy on Railway (FREE):
echo.
echo 1. Go to: https://railway.app
echo 2. Sign up with GitHub (free)
echo 3. Click "New Project" → "Deploy from GitHub repo"
echo 4. Select your HabitStory repository
echo 5. Railway will auto-detect Python and deploy
echo.
echo 🔧 Environment Variables to set in Railway:
echo    GEMINI_API_KEY=your_api_key_here
echo    SECRET_KEY=your_jwt_secret_here
echo    ENVIRONMENT=production
echo    DEBUG=false
echo.
echo 📱 Your app will be available at: https://your-app-name.railway.app
echo.
goto end

:render
echo.
echo 🎨 Render Deployment
echo.
echo 📋 Steps to deploy on Render (FREE):
echo.
echo 1. Go to: https://render.com
echo 2. Sign up with GitHub (free)
echo 3. Click "New" → "Web Service"
echo 4. Connect your GitHub repository
echo 5. Configure:
echo    - Build Command: pip install -r requirements.txt
echo    - Start Command: python -m uvicorn app.main:app --host 0.0.0.0 --port $PORT
echo.
echo 🔧 Environment Variables to set:
echo    GEMINI_API_KEY=your_api_key_here
echo    SECRET_KEY=your_jwt_secret_here
echo    ENVIRONMENT=production
echo    DEBUG=false
echo.
goto end

:heroku
echo.
echo 🟣 Heroku Deployment
echo.
echo 📋 Steps to deploy on Heroku (FREE tier limited):
echo.
echo 1. Go to: https://heroku.com
echo 2. Sign up (free)
echo 3. Install Heroku CLI
echo 4. Run: heroku create your-app-name
echo 5. Run: git push heroku main
echo.
echo ⚠️  Note: Heroku free tier has limitations
echo.
goto end

:manual
echo.
echo 📖 Manual Setup Instructions
echo.
echo 🔧 You'll need to:
echo 1. Choose a cloud provider (Railway recommended)
echo 2. Upload your backend folder
echo 3. Set environment variables
echo 4. Configure the start command
echo.
echo 📱 Then update your React Native app to use the cloud URL
echo.
goto end

:invalid_choice
echo ❌ Invalid choice. Please run the script again.
goto end

:end
echo.
echo 📋 Next steps after cloud deployment:
echo    1. Get your cloud app URL (e.g., https://your-app.railway.app)
echo    2. Update your React Native app to use this URL
echo    3. Publish to Expo: npm run publish
echo    4. Your app will work with your computer off!
echo.
echo 💡 Need help? Check: CLOUD_DEPLOYMENT_GUIDE.md
echo.
pause
