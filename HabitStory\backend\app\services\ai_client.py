"""
Unified AI client interface for HabitStory.
Supports both OpenAI and Gemini APIs with a common interface.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from enum import Enum
import logging
from sqlalchemy.ext.asyncio import AsyncSession

from ..database import get_db_session

logger = logging.getLogger(__name__)


class AIProvider(Enum):
    """Supported AI providers."""
    OPENAI = "openai"
    GEMINI = "gemini"


class AIMessage:
    """Standardized message format for AI interactions."""
    
    def __init__(self, role: str, content: str):
        self.role = role  # "system", "user", "assistant"
        self.content = content
    
    def to_dict(self) -> Dict[str, str]:
        return {"role": self.role, "content": self.content}


class AIResponse:
    """Standardized response format from AI providers."""
    
    def __init__(
        self,
        content: str,
        provider: AIProvider,
        model: str,
        tokens_used: Optional[int] = None,
        input_tokens: Optional[int] = None,
        output_tokens: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.content = content
        self.provider = provider
        self.model = model
        self.tokens_used = tokens_used
        self.input_tokens = input_tokens
        self.output_tokens = output_tokens
        self.metadata = metadata or {}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "response": self.content,
            "provider": self.provider.value,
            "model": self.model,
            "tokens_used": self.tokens_used,
            "input_tokens": self.input_tokens,
            "output_tokens": self.output_tokens,
            "metadata": self.metadata
        }


class BaseAIClient(ABC):
    """Abstract base class for AI clients."""
    
    def __init__(self, provider: AIProvider):
        self.provider = provider
    
    @abstractmethod
    async def generate_response(
        self,
        messages: List[AIMessage],
        temperature: float = 0.7,
        max_tokens: int = 2048,
        functions: Optional[List[Dict]] = None,
        user_id: Optional[str] = None
    ) -> AIResponse:
        """Generate a response from the AI provider."""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if the AI provider is healthy and accessible."""
        pass
    
    @abstractmethod
    def count_tokens(self, text: str) -> int:
        """Count tokens in the given text."""
        pass
    
    def create_system_message(self, content: str) -> AIMessage:
        """Create a system message."""
        return AIMessage("system", content)
    
    def create_user_message(self, content: str) -> AIMessage:
        """Create a user message."""
        return AIMessage("user", content)
    
    def create_assistant_message(self, content: str) -> AIMessage:
        """Create an assistant message."""
        return AIMessage("assistant", content)
    
    def create_conversation(
        self,
        system_prompt: str,
        user_prompt: str,
        conversation_history: Optional[List[AIMessage]] = None
    ) -> List[AIMessage]:
        """Create a conversation with system and user messages."""
        messages = []
        
        # Add system message
        if system_prompt:
            messages.append(self.create_system_message(system_prompt))
        
        # Add conversation history
        if conversation_history:
            messages.extend(conversation_history)
        
        # Add user message
        if user_prompt:
            messages.append(self.create_user_message(user_prompt))
        
        return messages


class AIClientFactory:
    """Factory for creating AI clients."""
    
    _clients: Dict[AIProvider, BaseAIClient] = {}
    
    @classmethod
    def register_client(cls, provider: AIProvider, client: BaseAIClient):
        """Register an AI client for a provider."""
        cls._clients[provider] = client
        logger.info(f"Registered AI client for {provider.value}")
    
    @classmethod
    def get_client(cls, provider: AIProvider) -> BaseAIClient:
        """Get an AI client for the specified provider."""
        if provider not in cls._clients:
            raise ValueError(f"No client registered for provider: {provider.value}")
        return cls._clients[provider]
    
    @classmethod
    def get_available_providers(cls) -> List[AIProvider]:
        """Get list of available AI providers."""
        return list(cls._clients.keys())
    
    @classmethod
    def get_default_client(cls) -> BaseAIClient:
        """Get the default AI client (Gemini preferred)."""
        if AIProvider.GEMINI in cls._clients:
            return cls._clients[AIProvider.GEMINI]
        elif AIProvider.OPENAI in cls._clients:
            return cls._clients[AIProvider.OPENAI]
        else:
            raise ValueError("No AI clients available")


class UnifiedAIService:
    """Unified service for AI operations across providers."""

    def __init__(self, primary_provider: AIProvider = AIProvider.GEMINI, enable_cache: bool = True):
        self.primary_provider = primary_provider
        self.factory = AIClientFactory
        self.enable_cache = enable_cache
    
    async def generate_response(
        self,
        system_prompt: str,
        user_prompt: str,
        temperature: float = 0.7,
        max_tokens: int = 2048,
        functions: Optional[List[Dict]] = None,
        user_id: Optional[str] = None,
        provider: Optional[AIProvider] = None,
        context_type: Optional[str] = None,
        use_cache: Optional[bool] = None
    ) -> AIResponse:
        """Generate a response using the specified or primary provider."""
        target_provider = provider or self.primary_provider
        should_use_cache = use_cache if use_cache is not None else self.enable_cache

        # Prepare parameters for caching
        cache_params = {
            "temperature": temperature,
            "max_tokens": max_tokens,
            "functions": functions or []
        }

        # Try to get cached response first
        if should_use_cache:
            try:
                from .ai_cache import ai_cache_service
                async with get_db_session() as db:
                    cached_response = await ai_cache_service.get_cached_response(
                        db=db,
                        provider=target_provider.value,
                        system_prompt=system_prompt,
                        user_prompt=user_prompt,
                        parameters=cache_params,
                        context_type=context_type,
                        user_id=user_id
                    )

                    if cached_response:
                        return AIResponse(
                            content=cached_response["content"],
                            provider=target_provider,
                            model=cached_response.get("model", "unknown"),
                            tokens_used=cached_response.get("tokens_used"),
                            input_tokens=cached_response.get("input_tokens"),
                            output_tokens=cached_response.get("output_tokens"),
                            metadata={
                                "cached": True,
                                "cache_created": cached_response.get("cache_created"),
                                "cache_accessed": cached_response.get("cache_accessed")
                            }
                        )
            except Exception as e:
                logger.warning(f"Cache retrieval failed: {e}")

        try:
            client = self.factory.get_client(target_provider)
            messages = client.create_conversation(system_prompt, user_prompt)

            response = await client.generate_response(
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                functions=functions,
                user_id=user_id
            )

            # Cache the response if caching is enabled
            if should_use_cache:
                try:
                    from .ai_cache import ai_cache_service
                    async with get_db_session() as db:
                        await ai_cache_service.cache_response(
                            db=db,
                            provider=target_provider.value,
                            system_prompt=system_prompt,
                            user_prompt=user_prompt,
                            parameters=cache_params,
                            response_content=response.content,
                            model=response.model,
                            tokens_used=response.tokens_used,
                            input_tokens=response.input_tokens,
                            output_tokens=response.output_tokens,
                            context_type=context_type,
                            user_id=user_id
                        )
                except Exception as e:
                    logger.warning(f"Cache storage failed: {e}")

            return response
            
        except Exception as e:
            logger.error(f"Error with {target_provider.value}: {e}")
            
            # Try fallback provider if primary fails
            if provider is None and len(self.factory.get_available_providers()) > 1:
                fallback_providers = [p for p in self.factory.get_available_providers() 
                                    if p != target_provider]
                
                for fallback in fallback_providers:
                    try:
                        logger.info(f"Trying fallback provider: {fallback.value}")
                        return await self.generate_response(
                            system_prompt, user_prompt, temperature, max_tokens,
                            functions, user_id, fallback
                        )
                    except Exception as fallback_error:
                        logger.error(f"Fallback {fallback.value} also failed: {fallback_error}")
                        continue
            
            raise e
    
    async def health_check_all(self) -> Dict[str, bool]:
        """Check health of all available providers."""
        results = {}
        
        for provider in self.factory.get_available_providers():
            try:
                client = self.factory.get_client(provider)
                results[provider.value] = await client.health_check()
            except Exception as e:
                logger.error(f"Health check failed for {provider.value}: {e}")
                results[provider.value] = False
        
        return results
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about available providers."""
        return {
            "primary_provider": self.primary_provider.value,
            "available_providers": [p.value for p in self.factory.get_available_providers()],
            "total_providers": len(self.factory.get_available_providers()),
            "cache_enabled": self.enable_cache
        }

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        if not self.enable_cache:
            return {"cache_enabled": False}

        try:
            from .ai_cache import ai_cache_service
            async with get_db_session() as db:
                return await ai_cache_service.get_cache_stats(db)
        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {"error": str(e)}

    async def invalidate_cache(
        self,
        context_type: Optional[str] = None,
        user_id: Optional[str] = None,
        provider: Optional[str] = None
    ) -> int:
        """Invalidate cached responses."""
        if not self.enable_cache:
            return 0

        try:
            from .ai_cache import ai_cache_service
            async with get_db_session() as db:
                return await ai_cache_service.invalidate_cache(
                    db=db,
                    context_type=context_type,
                    user_id=user_id,
                    provider=provider
                )
        except Exception as e:
            logger.error(f"Failed to invalidate cache: {e}")
            return 0


# Global unified AI service instance
unified_ai_service = UnifiedAIService()


# Export commonly used classes and functions
__all__ = [
    'AIProvider',
    'AIMessage',
    'AIResponse',
    'BaseAIClient',
    'AIClientFactory',
    'UnifiedAIService',
    'unified_ai_service'
]
